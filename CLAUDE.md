# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
Unity-based 2D top-down rogue-like inspired by Path of Exile with procedural world generation, gem-based skill system, and complex character progression.

**Unity Version**: 6000.2.0b3 | **Pipeline**: URP | **IDE**: VS Code | **Input System**: Unity Input System (new)

🔁 Wiedervermeidung / Klarheit
DRY – Don't Repeat Yourself
→ Wiederhole Code nicht. Extrahiere Wiederholungen in Funktionen, Klassen oder Module.

KISS – Keep It Simple, Stupid
→ Schreibe Code so einfach wie möglich. Vermeide unnötige Komplexität.

YAGNI – You Ain't Gonna Need It
→ Implementiere nur das, was du aktuell brauchst – kein Overengineering.

SoC – Separation of Concerns
→ Teile Code in klar abgegrenzte Verantwortlichkeiten (z. B. Daten, Logik, Darstellung).

🔄 Änderbarkeit / Wartbarkeit
SRP – Single Responsibility Principle (Teil von SOLID)
→ Jede Klasse/Funktion sollte nur eine einzige Aufgabe haben.

OCP – Open/Closed Principle
→ Code sollte offen für Erweiterung, aber geschlossen für Veränderung sein.

LSP – Liskov Substitution Principle
→ Subklassen sollten ohne Probleme überall einsetzbar sein, wo ihre Basisklasse verwendet wird.

ISP – Interface Segregation Principle
→ Verwende viele kleine, spezifische Interfaces statt eines großen.

DIP – Dependency Inversion Principle
→ High-Level-Module sollten nicht von Low-Level-Modulen abhängen; beide sollen von Abstraktionen abhängen.

### Performance Requirements (STRICT)
- **Zero-GC Gameplay**: Use object pooling, no `new` in Update loops
- **Spatial Optimization**: All entities use SpatialCollider for collision
- **Chunk-Based Loading**: World unloads distant chunks automatically
- **Particle Effects**: Use `Emit()` NEVER `Instantiate()`
- **Collision Queries**: Cache results, use spatial partitioning
- **Enemy Updates**: Staggered state updates (0.5s intervals)

### Code Organization
- **Namespaces**: Do not create custom ones
- **Always include**: `using Sirenix.OdinInspector;` for editor attributes
- **Component Pattern**: Single responsibility, interface-based communication

### Unity-Specific Requirements
- Never modify .meta files
- Use `[RequireComponent]` for dependencies
- Cache references in Awake/Start
- Use ScriptableObjects for game data
- Unsubscribe all events in OnDestroy/OnReturnToPool

## Claude Memories
- dont overengineer, follow the prompt
- schreibe nicht ungefragt debug funktionen
- Dont create Helper Create Prefab tools or testers
- Erstelle niemals fallbacks zu alten system wenn wir neuer erstellen, führe eine volle migration durch und lösche das alte immer
- Nutze NIEMALS OnTriggerEnter/Exit und OnCollisionEnter/Exit Events von unity, wir nutzen kein Rigidbody2d! Wir nutzen nur Die Raycast + Collider.

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
NEVER create Prefabs and meta files, ur task is to write C# scripts.

## Code Style & Patterns
- Use Sirenix.OdinInspector for all inspector attributes
- Cache component references in Awake/Start methods
- Never use 'new' keyword in Update/FixedUpdate loops
- Use object pooling for all runtime instantiation
- Follow zero-GC gameplay programming principles

## Performance Requirements
- Maintain 60 FPS on target mobile devices
- Zero garbage collection during gameplay
- Maximum 16ms frame time budget
- Efficient memory usage with object pooling

## Specialized Agents
- `unity-feature-implementer` - Implement Unity features from markdown plans
- `unity-docs-writer` - Create concise technical documentation  
- `unity-code-reviewer` - Review code for performance and GC optimization
- `unity-bug-analyzer` - Analyze console errors and implementation logic
- `gem-tooltip-expert` - Handle skill/support gem tooltip systems
- `feature-planner` - Plan and design new features for the project