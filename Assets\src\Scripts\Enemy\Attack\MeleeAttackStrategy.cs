using UnityEngine;
using System.Collections;

/// <summary>
/// Melee attack strategy for enemies. Deals direct contact damage to the player.
/// Enhanced with critical hits and modifier support.
/// </summary>
[System.Serializable]
public class MeleeAttackStrategy : IAttackStrategy
{
        [SerializeField] private float damage = 10f;
        [SerializeField] private float attackRange = 1.5f;
        [SerializeField] private float attackCooldown = 1.0f;
        [SerializeField] private CollisionLayers projectileLayer = CollisionLayers.EnemyProjectile;
        [SerializeField] private string animationName = "attack";
        [SerializeField] private bool requiresAnimation = true;
#if UNITY_EDITOR
        [SerializeField, Tooltip("Enable debug logs for MeleeAttackStrategy (Editor only).")]
        private bool enableDebugLogs = false;
#endif
        
        // Modifier system
        private AttackModifierCollection modifiers = new AttackModifierCollection();
        
        // Cooldown management
        private float lastAttackTime = -999f;
        
        // Animation event handling
        private Transform currentAttacker;
        private Transform currentTarget;
        private System.Action attackCompletionCallback;
        private bool damageDealt = false;
        
        // Cached components (set via SetupComponents)
        private SpriteAnimator cachedSpriteAnimator;
        private EnemyAnimationController cachedAnimationController;
        
        // IAttackStrategy Implementation
        public float Damage 
        { 
            get => (float)modifiers.CalculateDamage(damage).finalDamage; 
            set => damage = value; 
        }
        
        public CollisionLayers ProjectileLayer
        {
            get => projectileLayer;
            set => projectileLayer = value;
        }
        
        public float AttackRange => modifiers.GetModifiedRange(attackRange);
        public float AttackCooldown => CalculateIntelligentCooldown();
        public string AttackAnimationName => animationName;
        public bool RequiresAnimation => requiresAnimation;
        public AttackModifierCollection Modifiers => modifiers;
        
        // Cooldown properties (using modified cooldown)
        public bool IsOnCooldown => Time.time - lastAttackTime < AttackCooldown;
        public float CooldownRemaining => Mathf.Max(0f, AttackCooldown - (Time.time - lastAttackTime));
        
        public bool CanAttack(Transform target, float distanceToTarget)
        {
            if (target == null) return false;
            if (distanceToTarget > AttackRange) return false;
            if (IsOnCooldown) return false;
            
            // Check if target is the player using PlayerManager
            if (target.gameObject == PlayerManager.PlayerGameObject)
            {
                return true;
            }
            
            // Check if target is player using tag
            if (target.CompareTag("Player"))
            {
                return true;
            }
            
            return false;
        }
        
        public void ExecuteAttackWithAnimation(Transform attacker, Transform target, System.Action onComplete = null)
        {
            if (!CanAttack(target, Vector3.Distance(attacker.position, target.position)))
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: Cannot attack - CanAttack returned false");
#endif
                onComplete?.Invoke();
                return;
            }
            
#if UNITY_EDITOR
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: Starting attack execution, RequiresAnimation: {RequiresAnimation}, AnimationName: '{animationName}'");
#endif
            
            // Start cooldown immediately to prevent rapid attacks if interrupted
            StartCooldown();
            
            if (RequiresAnimation)
            {
                // Handle animation-based attack with frame events
                if (cachedSpriteAnimator != null && cachedSpriteAnimator.HasAnimation(animationName))
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: Found SpriteAnimator and animation '{animationName}', using timeout fallback");
#endif
                    
                    // Store target and completion callback for frame event handling
                    currentAttacker = attacker;
                    currentTarget = target;
                    attackCompletionCallback = onComplete;
                    damageDealt = false;
                    
                    // First, clean up any existing event subscriptions to avoid double-triggers
                    cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                    
                    // Apply attack speed modifier to animation
                    float attackSpeedBonus = modifiers.GetAttackSpeedBonus();
                    float animationSpeedMultiplier = 1f + attackSpeedBonus;
                    cachedSpriteAnimator.SetTemporarySpeedMultiplier(animationSpeedMultiplier);
                    
                    // EnemyAnimationController is interfering - disable it during attack
                    if (cachedAnimationController != null)
                    {
                        cachedAnimationController.enabled = false;
#if UNITY_EDITOR
                        if (enableDebugLogs)
                            UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: Disabled EnemyAnimationController to prevent interference");
#endif
                    }
                    
                    // Subscribe to animation events BEFORE starting the animation
                    // This ensures we catch all events from the new animation
                    cachedSpriteAnimator.OnAnimationEvent += OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted += OnAnimationCompleted;
                    
                    // Use PlayForced to ensure animation starts from beginning for precise frame events
                    cachedSpriteAnimator.PlayForced(animationName);
                    
                    // Since SpriteAnimator OnAnimationCompleted is buggy, use a timer-based fallback
                    if (cachedSpriteAnimator != null)
                    {
                        cachedSpriteAnimator.StartCoroutine(CheckAnimationAfterDelay(attacker.name));
                        // Start a timer for expected animation duration (most attack animations are ~1 second)
                        cachedSpriteAnimator.StartCoroutine(AnimationCompletionTimer(1.0f));
                    }
                    
#if UNITY_EDITOR
                    if (enableDebugLogs)
                    {
                        UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: Started attack animation '{animationName}', subscribed to events, waiting for frame events...");
                        UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: Animation state - IsPlaying: {cachedSpriteAnimator.IsPlaying}, CurrentAnimation: '{cachedSpriteAnimator.CurrentAnimation}'");
                        UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: EnemyAnimationController enabled: {cachedAnimationController?.enabled ?? false}");
                    }
#endif
                }
                else
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: No SpriteAnimator or animation '{animationName}' found, executing immediate attack");
#endif
                    
                    // No animation available, execute immediately
                    PerformDamage(target, attacker.name);
                    onComplete?.Invoke();
                }
            }
            else
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {attacker.name}: No animation required, executing immediate attack");
#endif
                
                // No animation required, execute immediately
                PerformDamage(target, attacker.name);
                onComplete?.Invoke();
            }
        }
        
        public void ExecuteAttack(Transform attacker, Transform target)
        {
            // Legacy method - calls new method without completion callback
            ExecuteAttackWithAnimation(attacker, target, null);
        }
        
        public void OnSpawnFromPool()
        {
            // Reset cooldown on spawn
            lastAttackTime = -999f;
            
            // Clear any runtime modifiers to avoid stacking across spawns
            modifiers.ClearModifiers();
        }
        
        public void OnReturnToPool()
        {
            // No special cleanup needed for melee attacks
        }
        
        /// <summary>
        /// Constructor for creating melee attack strategies programmatically.
        /// </summary>
        public MeleeAttackStrategy() { }
        
        /// <summary>
        /// Constructor with custom parameters.
        /// </summary>
        public MeleeAttackStrategy(float damage, float attackRange = 1.5f, float attackCooldown = 1.0f, string animationName = "attack")
        {
            this.damage = damage;
            this.attackRange = attackRange;
            this.attackCooldown = attackCooldown;
            this.animationName = animationName;
            this.lastAttackTime = -999f;
        }
        
        public void StartCooldown()
        {
            lastAttackTime = Time.time;
        }
        
        /// <summary>
        /// Update attack modifiers from external sources (chunk buffs, etc.)
        /// </summary>
        public void UpdateModifiers(System.Collections.Generic.List<AttackModifier> externalModifiers)
        {
            // Clear existing external modifiers
            modifiers.ClearModifiers("chunk");
            modifiers.ClearModifiers("distance_scaling");
            modifiers.ClearModifiers("random");
            modifiers.ClearModifiers("elite_zone");
            
            // Add new external modifiers in batch (more efficient)
            modifiers.AddModifiers(externalModifiers);
        }
        
        /// <summary>
        /// Setup the attack strategy with required components.
        /// Called once during initialization to cache all needed components.
        /// </summary>
        public void SetupComponents(SpriteAnimator spriteAnimator, EnemyAnimationController animationController)
        {
            cachedSpriteAnimator = spriteAnimator;
            cachedAnimationController = animationController;
        }
        
        /// <summary>
        /// Calculate intelligent cooldown that ensures animation can complete.
        /// Uses the longer of: base cooldown or animation duration.
        /// Handles edge cases where no animation is required.
        /// </summary>
        private float CalculateIntelligentCooldown()
        {
            // Get base cooldown with modifiers applied
            float baseCooldown = modifiers.GetModifiedCooldown(attackCooldown);
            
            // EDGE CASE: If no animation is required, use base cooldown only
            if (!requiresAnimation || string.IsNullOrEmpty(animationName))
            {
                return baseCooldown;
            }
            
            // Get animation duration with all speed modifiers
            float animationDuration = GetCurrentAnimationDuration();
            
            // EDGE CASE: If animation duration can't be determined, fallback to base cooldown
            if (animationDuration <= 0f)
            {
                return baseCooldown;
            }
            
            // Use the longer of base cooldown or animation duration
            // This ensures animation never gets cut off, but allows for longer cooldowns if desired
            float intelligentCooldown = Mathf.Max(baseCooldown, animationDuration);
            
#if UNITY_EDITOR
            if (enableDebugLogs && Mathf.Abs(intelligentCooldown - baseCooldown) > 0.01f)
            {
                UnityEngine.Debug.Log($"[MeleeAttack] Intelligent cooldown: base={baseCooldown:F2}s, animation={animationDuration:F2}s, final={intelligentCooldown:F2}s");
            }
#endif
            
            return intelligentCooldown;
        }
        
        /// <summary>
        /// Get the current animation duration including all speed modifiers.
        /// Returns 0 if no animation or sprite animator available.
        /// </summary>
        private float GetCurrentAnimationDuration()
        {
            if (cachedSpriteAnimator == null || string.IsNullOrEmpty(animationName))
                return 0f;
            
            // Use the specific animation name for this attack strategy
            return cachedSpriteAnimator.GetAnimationDuration(animationName);
        }
        
        /// <summary>
        /// Cancel the currently executing attack.
        /// Stops animation, cleans up events, and restores EnemyAnimationController.
        /// </summary>
        public void CancelAttack()
        {
            // Only cancel if we have an active attack in progress
            if (currentAttacker == null || attackCompletionCallback == null)
            {
                return;
            }
            
#if UNITY_EDITOR
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Cancelling attack due to target out of range");
#endif
            
            // Stop current animation and restore EnemyAnimationController
            if (cachedSpriteAnimator != null)
            {
                // Unsubscribe from events to prevent callbacks
                cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                
                // Stop all running timer coroutines to prevent race conditions
                cachedSpriteAnimator.StopAllCoroutines();
                
                // Clear temporary speed override
                cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
                
                // Stop current animation (let EnemyAnimationController take over)
                cachedSpriteAnimator.Stop();
            }
            
            // Re-enable EnemyAnimationController immediately and restore appropriate animation
            if (cachedAnimationController != null)
            {
                cachedAnimationController.enabled = true;
                
                // Force immediate animation restoration to prevent visual sticking
                // This ensures the agent immediately shows the correct movement animation
                if (currentAttacker != null)
                {
                    var movement = currentAttacker.GetComponent<PathfindingMovement>();
                    if (movement != null)
                    {
                        movement.RestoreStateAnimation();
                    }
                }
                
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Re-enabled EnemyAnimationController and restored state animation after cancel");
#endif
            }
            
            // DO NOT invoke the completion callback since this is a cancellation
            // The movement system should handle the cancellation appropriately
            
            // Clean up attack state
            CleanupAttackState();
        }
        
        private void PerformDamage(Transform target, string attackerName)
        {
            // Check if target is the player using PlayerManager
            bool isPlayer = false;
            if (target.gameObject == PlayerManager.PlayerGameObject)
            {
                isPlayer = true;
            }
            // Check if target is player using tag
            else if (target.CompareTag("Player"))
            {
                isPlayer = true;
            }
            
            if (isPlayer)
            {
                // Calculate damage with modifiers (including crit)
                CriticalHitResult damageResult = modifiers.CalculateDamage(damage);
                
#if UNITY_EDITOR
                if (enableDebugLogs)
                {
                    if (damageResult.isCritical)
                    {
                        UnityEngine.Debug.Log($"{attackerName}: CRITICAL HIT! {damageResult.finalDamage} damage (x{damageResult.critMultiplier:F1})");
                    }
                    else
                    {
                        UnityEngine.Debug.Log($"{attackerName}: Melee hit for {damageResult.finalDamage} damage");
                    }
                }
#endif
                
                // Create damage info for melee attack (always physical) with breakdown
                DamageInfo damageInfo = DamageInfo.FromSingleType(
                    damageResult.finalDamage,
                    DamageType.Physical,
                    damageResult.isCritical,
                    damageResult.critMultiplier,
                    $"Melee_{attackerName}"
                );
                
                bool success = PlayerManager.DealDamageToPlayer(damageInfo);
                
#if UNITY_EDITOR
                if (enableDebugLogs && !success)
                {
                    UnityEngine.Debug.LogWarning($"{attackerName}: Melee attack failed - PlayerManager returned false");
                }
#endif
            }
        }
        
        /// <summary>
        /// Handles animation frame events for precise damage timing.
        /// Listens for 'hit', 'strike', or 'impact' events.
        /// </summary>
        private void OnAnimationFrameEvent(string eventName, string parameter)
        {
#if UNITY_EDITOR
            // Log ALL frame events to see what's happening
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker?.name}: Animation frame event '{eventName}' received, damageDealt: {damageDealt}, currentAttacker: {currentAttacker != null}, currentTarget: {currentTarget != null}");
#endif
            
            // Check for hit events (case-insensitive)
            string lowerEventName = eventName.ToLowerInvariant();
            if ((lowerEventName == "hit" || lowerEventName == "strike" || lowerEventName == "impact") 
                && !damageDealt && currentAttacker != null && currentTarget != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Dealing damage from animation event '{eventName}'");
#endif
                // Deal damage at exact frame
                PerformDamage(currentTarget, currentAttacker.name);
                damageDealt = true;
                
            }
#if UNITY_EDITOR
            else if (enableDebugLogs)
            {
                string reason = damageDealt ? "already dealt damage" : 
                               currentAttacker == null ? "no attacker" : 
                               currentTarget == null ? "no target" : 
                               "event not recognized";
                UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker?.name}: Animation event '{eventName}' ignored - {reason}");
            }
#endif
        }
        
        /// <summary>
        /// Handles animation completion for cleanup.
        /// </summary>
        private void OnAnimationCompleted(string animationName)
        {
#if UNITY_EDITOR
            // Log ALL animation completions to see what's happening
            if (enableDebugLogs)
                UnityEngine.Debug.Log($"[MeleeAttack] OnAnimationCompleted: '{animationName}', expected: '{this.animationName}', currentAttacker: {currentAttacker != null}, callback: {attackCompletionCallback != null}");
#endif
            
            // Only process if this is our attack animation AND we have an active attack in progress
            if (animationName == this.animationName && currentAttacker != null && attackCompletionCallback != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Animation '{animationName}' completed - processing cleanup");
#endif
                
                // Immediately unsubscribe to prevent further events
                if (currentAttacker != null)
                {
                    if (cachedSpriteAnimator != null)
                    {
                        cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                        cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                        
                        // Clear temporary speed override
                        cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
                    }
                    
                    // Re-enable EnemyAnimationController
                    if (cachedAnimationController != null)
                    {
                        cachedAnimationController.enabled = true;
#if UNITY_EDITOR
                        if (enableDebugLogs)
                            UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Re-enabled EnemyAnimationController");
#endif
                    }
                }
                
                
                // If no frame event dealt damage, deal it now (fallback)
                if (!damageDealt && currentAttacker != null && currentTarget != null)
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Animation completed without frame event - using fallback damage");
#endif
                    PerformDamage(currentTarget, currentAttacker.name);
                }
#if UNITY_EDITOR
                else if (enableDebugLogs)
                {
                    string reason = damageDealt ? "already dealt via frame event" : 
                                   currentAttacker == null ? "no attacker" : 
                                   "no target";
                    UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker?.name}: Animation completed - fallback skipped: {reason}");
                }
#endif
                
                // Notify completion and cleanup
                attackCompletionCallback?.Invoke();
                CleanupAttackState();
            }
            else
            {
#if UNITY_EDITOR
                if (enableDebugLogs && animationName == this.animationName)
                {
                    // Only log if it's our attack animation but other conditions aren't met
                    string reason = currentAttacker == null ? "no current attacker" :
                                   attackCompletionCallback == null ? "no completion callback" :
                                   "unknown";
                    UnityEngine.Debug.Log($"[MeleeAttack] Ignoring animation completion - {reason}");
                }
#endif
            }
        }
        
        /// <summary>
        /// Cleanup attack state variables.
        /// </summary>
        private void CleanupAttackState()
        {
            // ALWAYS unsubscribe from events to prevent them from triggering after attack
            if (cachedSpriteAnimator != null)
            {
                cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
            }
            
            currentAttacker = null;
            currentTarget = null;
            attackCompletionCallback = null;
            damageDealt = false;
        }
        
        /// <summary>
        /// Debug coroutine to check if animation changes after a delay
        /// </summary>
        private System.Collections.IEnumerator CheckAnimationAfterDelay(string attackerName)
        {
            yield return new WaitForSeconds(0.1f); // Wait a few frames
            
#if UNITY_EDITOR
            if (enableDebugLogs && cachedSpriteAnimator != null)
            {
                UnityEngine.Debug.Log($"[MeleeAttack] {attackerName}: After delay - IsPlaying: {cachedSpriteAnimator.IsPlaying}, CurrentAnimation: '{cachedSpriteAnimator.CurrentAnimation}'");
            }
#endif
        }
        
        /// <summary>
        /// Timer-based animation completion fallback due to SpriteAnimator bug
        /// </summary>
        private System.Collections.IEnumerator AnimationCompletionTimer(float duration)
        {
            yield return new WaitForSeconds(duration);
            
            // Only complete if we haven't been cleaned up already
            if (currentAttacker != null && attackCompletionCallback != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Timer-based animation completion triggered after {duration}s");
#endif
                
                // If no frame event dealt damage, deal it now (fallback)
                if (!damageDealt && currentAttacker != null && currentTarget != null)
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: No frame event detected - using timer fallback damage");
#endif
                    PerformDamage(currentTarget, currentAttacker.name);
                }
                
#if UNITY_EDITOR
                if (enableDebugLogs)
                    UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Attack cooldown remaining: {CooldownRemaining:F1}s");
#endif
                
                // Immediately unsubscribe to prevent multiple calls
                if (cachedSpriteAnimator != null)
                {
                    cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                    
                    // Clear temporary speed override
                    cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
                }
                
                // Re-enable EnemyAnimationController
                if (currentAttacker != null)
                {
                    if (cachedAnimationController != null)
                    {
                        cachedAnimationController.enabled = true;
#if UNITY_EDITOR
                        if (enableDebugLogs)
                            UnityEngine.Debug.Log($"[MeleeAttack] {currentAttacker.name}: Re-enabled EnemyAnimationController via timer");
#endif
                    }
                }
                
                // Notify completion and cleanup
                attackCompletionCallback?.Invoke();
                CleanupAttackState();
            }
        }
    }