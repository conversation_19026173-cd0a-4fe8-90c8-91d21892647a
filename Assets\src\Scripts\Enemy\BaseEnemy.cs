using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Linq;
using System.Collections;


/// <summary>
/// Types of enemies in the game
/// </summary>
public enum EnemyType
{
    // Generic types
    Basic,
    Boss,
    
    // Breach-specific types for themed splinter drops
    Fire,      // Fire/Red Breach enemies
    Ice,       // Ice/Blue Breach enemies  
    Nature,    // Nature/Green Breach enemies
    Lightning, // Lightning/Yellow Breach enemies
    Chaos,     // Chaos Breach enemies
    Shadow     // Shadow/Purple Breach enemies
}

/// <summary>
/// Base class for all enemy types. Provides common functionality like
/// health management, movement delegation, and object pooling.
/// </summary>
[RequireComponent(typeof(Collider2D))]
[RequireComponent(typeof(CombatantHealth))]
public abstract class BaseEnemy : MonoBehaviour, ISpawnable
{
        [Title("Enemy Type")]
        [SerializeField] protected EnemyType enemyType = EnemyType.Basic;
        
        [Title("Attack System")]
        [SerializeReference]
        [InfoBox("Attack strategy for this enemy. If null, will be created automatically based on movement type.")]
        protected IAttackStrategy attackStrategy;
        
        
        
        [Title("Rewards")]
        [SerializeField]
        [Range(1, 1000)]
        [Tooltip("Experience points granted when this enemy is killed")]
        protected int experienceReward = 10;
        
        [SerializeField]
        [Tooltip("Multiply experience based on distance from spawn (e.g., 0.1 = +10% per chunk)")]
        [Range(0f, 1f)]
        protected float experienceDistanceScaling = 0.1f;
        
        // DEPRECATED: Splinter drops are now handled exclusively through BreachEffect system
        // Only enemies spawned by breaches can drop splinters, using the breach's configuration
        // [SerializeField]
        // [InfoBox("Configuration for splinter drops. Leave empty to use global default from SplinterCollectionManager.")]
        // [Tooltip("Optional splinter drop configuration for this enemy. If null, will attempt to find global config.")]
        // protected SplinterDropConfig splinterDropConfig;
        
        [Title("Independent Currency Drops")]
        [SerializeField]
        [Tooltip("Enable currency drops for this enemy when killed (independent of breach spawning)")]
        protected bool enableIndependentCurrencyDrops = false;
        
        [ShowIf("enableIndependentCurrencyDrops")]
        [SerializeField]
        [Required]
        [Tooltip("Currency configuration for drop behavior and amounts")]
        protected CurrencyConfig independentCurrencyConfig;
        
        [ShowIf("enableIndependentCurrencyDrops")]
        [SerializeField]
        [Range(0f, 1f)]
        [Tooltip("Base chance for currency drops (0.0 = never, 1.0 = always)")]
        protected float independentCurrencyDropChance = 0.25f;
        
        
        
        // Public properties for attack configuration access - delegate to strategy
        public float AttackRange => attackStrategy?.AttackRange ?? 1.5f;
        public AttackType AttackType => attackStrategy is MeleeAttackStrategy ? AttackType.Melee : AttackType.Ranged;
        public float AttackCooldown => attackStrategy?.AttackCooldown ?? 1.0f;
        public float Damage => attackStrategy?.Damage ?? 10f;
        
        // Public access to attack strategy properties for movement system
        public bool IsAttackOnCooldown => attackStrategy?.IsOnCooldown ?? false;
        public float AttackCooldownRemaining => attackStrategy?.CooldownRemaining ?? 0f;
        
        [Title("Target Selection")]
        [SerializeField, InfoBox("How this enemy selects target points on the player")]
        protected TargetSelectionMode targetSelectionMode = TargetSelectionMode.Nearest;
        
        [SerializeField, ShowIf("@targetSelectionMode == TargetSelectionMode.PreferType")]
        [InfoBox("Preferred target point types. Will fall back to any available if none match.")]
        protected PlayerTargetPoint.TargetPointType[] preferredTargetTypes = { PlayerTargetPoint.TargetPointType.Center };
        
        [SerializeField]
        [InfoBox("If true, shows debug lines to the current target point")]
        protected bool showTargetDebug = false;
        
        [SerializeField]
        [Tooltip("Enable debug logging for BaseEnemy")]
        protected bool enableEnemyDebugLogging = false;
        
        [Title("References")]
        [Required]
        [SerializeField] protected SpriteRenderer spriteRenderer;
        
        // Components
        protected CombatantHealth enemyHealth;
        protected Collider2D col2D;
        protected IEnemyMovement movementStrategy;
        protected SpriteAnimator spriteAnimator;
        
        // State
        protected PlayerTargetPoint currentTargetPoint;
        protected bool isDead = false;
        
        // Performance optimization
        private float targetSelectionTimer = 0f;
        private const float TARGET_SELECTION_INTERVAL = 0.2f; // Update target every 0.2 seconds
        
        // Chunk buff system
        private Vector3Int currentChunk = Vector3Int.zero;
        private float chunkUpdateTimer = 0f;
        private const float CHUNK_UPDATE_INTERVAL = 1.0f; // Update chunk buffs every second
        
        // Pending chunk modifiers to apply when attack strategy becomes available
        private List<AttackModifier> pendingChunkModifiers;
        
        // Guard against multiple completion callbacks
        private bool attackCompletionInProgress = false;
        
        // Properties
        public EnemyType Type => enemyType;
        public CombatantHealth Health => enemyHealth;
        public bool IsAlive => enemyHealth != null && enemyHealth.CurrentHealth > 0 && !isDead;
        public Transform PlayerTransform => PlayerManager.PlayerTransform;
        public Vector3 CurrentTargetPosition => GetCurrentTargetPosition();
        
        /// <summary>
        /// Sets the enemy type at runtime (used by breach spawning system)
        /// </summary>
        public void SetEnemyType(EnemyType newEnemyType)
        {
            enemyType = newEnemyType;
            
            if (enableEnemyDebugLogging)
            {
                Debug.Log($"[BaseEnemy] {gameObject.name}: Enemy type set to {newEnemyType}");
            }
        }
        
        /// <summary>
        /// Gets the position that this enemy should target. Movement strategies can use this
        /// instead of directly using PlayerTransform.position for more accurate targeting.
        /// </summary>
        public Vector3 GetTargetPosition() => GetCurrentTargetPosition();        
        protected virtual void Awake()
        {
            // Cache components
            enemyHealth = GetComponent<CombatantHealth>();
            col2D = GetComponent<Collider2D>();
            spriteAnimator = GetComponent<SpriteAnimator>();            
            // Get movement strategy
            movementStrategy = GetMovementStrategy();
            
            
            // Initialize attack strategy if not manually assigned
            if (attackStrategy == null && movementStrategy != null && movementStrategy.CanAttack)
            {
                // Default to basic melee strategy if none configured
                Debug.LogWarning($"{name}: No attack strategy configured. Creating default melee strategy. For custom behavior, assign an attack strategy in the inspector.");
                attackStrategy = AttackStrategyFactory.CreateMelee(
                    damage: 10f,
                    attackRange: 1.5f,
                    attackCooldown: 1.0f,
                    animationName: "attack"
                );
            }
            
            // Setup attack strategy with cached components (both auto-created and manually assigned)
            if (attackStrategy != null)
            {
                var animationController = GetComponent<EnemyAnimationController>();
                attackStrategy.SetupComponents(spriteAnimator, animationController);
                
                // Apply any pending chunk modifiers that were stored while attack strategy was null
                if (pendingChunkModifiers != null)
                {
                    attackStrategy.UpdateModifiers(pendingChunkModifiers);
                    
                    if (enableEnemyDebugLogging)
                    {
                        int projectileModifierCount = 0;
                        for (int i = 0; i < pendingChunkModifiers.Count; i++)
                        {
                            if (pendingChunkModifiers[i].type == AttackModifierType.ProjectileCount)
                                projectileModifierCount++;
                        }
                        Debug.Log($"[BaseEnemy] {name}: Applied {pendingChunkModifiers.Count} pending chunk modifiers (including {projectileModifierCount} projectile modifiers)");
                    }
                    
                    pendingChunkModifiers = null; // Clear pending modifiers after application
                }
            }
            
            // Subscribe to attack events if movement supports it
            if (movementStrategy != null)
            {
                movementStrategy.OnAttackRequested += ExecuteAttack;
            }
            
            // Subscribe to events
            if (enemyHealth != null)
            {
                enemyHealth.OnDeath.AddListener(HandleDeath);
            }
            
            // Auto-find sprite renderer if not assigned
            if (spriteRenderer == null)
            {
                spriteRenderer = GetComponentInChildren<SpriteRenderer>();
            }
        }
        
        protected virtual void Start()
        {
            // Initialize movement
            movementStrategy?.Initialize();
        }
        
        protected virtual void Update()
        {
            if (!IsAlive || PlayerManager.PlayerTransform == null) return;
            
            // Update target selection with interval for performance
            targetSelectionTimer += Time.deltaTime;
            if (targetSelectionTimer >= TARGET_SELECTION_INTERVAL)
            {
                targetSelectionTimer = 0f;
                UpdateTargetSelection();
            }
            
            // Update chunk buffs with interval for performance
            chunkUpdateTimer += Time.deltaTime;
            if (chunkUpdateTimer >= CHUNK_UPDATE_INTERVAL)
            {
                chunkUpdateTimer = 0f;
                UpdateChunkBuffs();
            }
            
            // Update movement
            if (movementStrategy != null)
            {
                // Pass player transform from PlayerManager
                movementStrategy.UpdateMovement(PlayerManager.PlayerTransform);
            }
            
            // Debug visualization
            if (showTargetDebug && Application.isPlaying)
            {
                DrawTargetDebug();
            }
        }
        
        /// <summary>
        /// Get the movement strategy for this enemy type.
        /// Must be implemented by derived classes.
        /// </summary>
        protected abstract IEnemyMovement GetMovementStrategy();
        
        // NOTE: Unity Physics2D collision events removed - contact damage is handled by attack strategies using manual collision detection
        
        
        /// <summary>
        /// Execute attack using the attack strategy. Called by movement system.
        /// </summary>
        protected virtual void ExecuteAttack(Transform target)
        {
            if (target == null || !IsAlive) return;
            
            // Require attack strategy - no legacy fallback
            if (attackStrategy == null)
            {
                OnAttackExecutionComplete();
                return;
            }
            
            // Check if attack strategy is ready (includes cooldown check)
            if (!attackStrategy.IsOnCooldown)
            {
                // Execute with animation coordination and completion callback
                attackStrategy.ExecuteAttackWithAnimation(transform, target, OnAttackExecutionComplete);
            }
            else
            {
                // Notify movement that attack was rejected due to cooldown
                OnAttackExecutionComplete();
            }
        }
        
        /// <summary>
        /// Cancel the currently executing attack. Called by movement system when target moves out of range.
        /// This will stop any ongoing attack animation and clean up the attack state.
        /// </summary>
        public virtual void CancelAttack()
        {
            if (attackStrategy != null)
            {
                attackStrategy.CancelAttack();
            }
        }
        
        /// <summary>
        /// Called when attack execution is complete through the attack strategy system.
        /// Notifies the movement system that it can transition out of attack state.
        /// </summary>
        protected virtual void OnAttackExecutionComplete()
        {
            // Guard against multiple calls within same attack cycle
            if (attackCompletionInProgress)
            {
                return;
            }
            
            attackCompletionInProgress = true;
            
            // Notify PathfindingMovement that attack is complete
            if (movementStrategy is PathfindingMovement pathfindingMovement)
            {
                pathfindingMovement.OnAttackCompleted();
            }
            
            // Reset guard flag after a small delay to allow for next attack cycle
            StartCoroutine(ResetAttackCompletionGuard());
            
            // For other movement types, no action needed as they don't track attack state
        }
        
        private System.Collections.IEnumerator ResetAttackCompletionGuard()
        {
            // Wait longer to prevent race conditions between timeout and animation completion
            yield return new WaitForSeconds(0.1f);
            attackCompletionInProgress = false;
        }
        
        protected virtual void HandleDeath()
        {
            isDead = true;
            
            // Stop movement
            movementStrategy?.StopMovement();
            
            // Disable collider
            if (col2D != null)
            {
                col2D.enabled = false;
            }
            
            // Additional death logic can be added in derived classes
            OnDeath();
        }
        
        /// <summary>
        /// Called when the enemy dies. Can be overridden for custom death behavior.
        /// </summary>
        protected virtual void OnDeath()
        {
            // Grant experience to the player
            GrantExperienceToPlayer();
            
            // Handle currency drops
            HandleCurrencyDrops();
            
            // Handle splinter drops
            HandleSplinterDrops();
            
            // Notify buff system about enemy kill
            NotifyBuffSystemOfKill();
            
            // Notify progress tracker for unlock system
            NotifyProgressTracker();
            
            // Override in derived classes for specific death behavior
        }
        
        private void GrantExperienceToPlayer()
        {
            var playerStats = PlayerManager.PlayerStats;
            if (playerStats != null && experienceReward > 0)
            {
                // Calculate scaled experience based on distance
                float finalExperience = experienceReward;
                
                if (experienceDistanceScaling > 0)
                {
                    // Get distance from spawn
                    var chunkManager = TilemapChunkManager.Instance;
                    if (chunkManager != null)
                    {
                        ChunkCoordinate currentChunk = ChunkCoordinate.FromWorldPosition(transform.position, chunkManager.GetChunkWidth(), chunkManager.GetChunkHeight());
                        int distance = Mathf.Abs(currentChunk.x) + Mathf.Abs(currentChunk.y);
                        
                        // Apply distance scaling
                        float multiplier = 1f + (distance * experienceDistanceScaling);
                        finalExperience = experienceReward * multiplier;
                    }
                }
                
                playerStats.AddXP(Mathf.RoundToInt(finalExperience));
              
            }
        }
        
        private void HandleCurrencyDrops()
        {
            // Handle independent currency drops for all enemies (breach-spawned and regular)
            if (enableIndependentCurrencyDrops && independentCurrencyConfig != null)
            {
                HandleIndependentCurrencyDrops();
            }
        }
        
        
        private void HandleIndependentCurrencyDrops()
        {
            // Calculate drop chance with distance scaling
            float dropChance = independentCurrencyDropChance;
            var chunkManager = TilemapChunkManager.Instance;
            if (chunkManager != null)
            {
                ChunkCoordinate currentChunk = ChunkCoordinate.FromWorldPosition(transform.position, chunkManager.GetChunkWidth(), chunkManager.GetChunkHeight());
                int distanceFromSpawn = Mathf.Abs(currentChunk.x) + Mathf.Abs(currentChunk.y);
                
                // Apply same distance scaling as breach drops
                float distanceMultiplier = 1f + (distanceFromSpawn * 0.1f);
                dropChance *= distanceMultiplier;
            }
            
            // Roll for currency drop
            if (Random.Range(0f, 1f) <= dropChance)
            {
                int currencyAmount = independentCurrencyConfig.GetRandomEnemyDropAmount();
                SpawnCurrencyPickup(independentCurrencyConfig, currencyAmount);
                
                if (enableEnemyDebugLogging)
                {
                    Debug.Log($"[BaseEnemy] {gameObject.name}: Independent enemy dropped {currencyAmount} currency");
                }
            }
        }
        
        private void SpawnCurrencyPickup(CurrencyConfig currencyConfig, int amount)
        {
            if (currencyConfig?.currencyPickupPrefab == null)
            {
                Debug.LogError($"[BaseEnemy] {gameObject.name}: Cannot spawn currency pickup - no prefab assigned");
                return;
            }
            
            var poolManager = PoolManager.Instance;
            if (poolManager == null)
            {
                Debug.LogError($"[BaseEnemy] {gameObject.name}: Cannot spawn currency pickup - PoolManager not found");
                return;
            }
            
            // Get scattered spawn position
            Vector3 spawnPosition = currencyConfig.GetEnemyScatterPosition(transform.position);
            
            // Spawn currency pickup
            string prefabName = currencyConfig.currencyPickupPrefab.name;
            var pickupObject = poolManager.Spawn(prefabName, spawnPosition, Quaternion.identity);
            
            if (pickupObject != null)
            {
                // Set currency value
                var currencyPickup = pickupObject.GetComponent<CurrencyPickup>();
                if (currencyPickup != null)
                {
                    currencyPickup.SetCurrencyValue(amount);
                }
                
                if (enableEnemyDebugLogging)
                {
                    Debug.Log($"[BaseEnemy] {gameObject.name}: Spawned currency pickup with value {amount} at {spawnPosition}");
                }
            }
            else
            {
                Debug.LogWarning($"[BaseEnemy] {gameObject.name}: Failed to spawn currency pickup '{prefabName}' - check PoolManager registration");
            }
        }
        
        private void HandleSplinterDrops()
        {
            // BREACH-ONLY SPLINTER DROPS: Only enemies spawned by breaches can drop splinters
            var breachMarker = GetComponent<BreachSpawnedMarker>();
            if (breachMarker == null)
            {
                // Not breach-spawned = no splinter drops
                return;
            }
            
            // Check if splinter system is available
            var splinterManager = SplinterCollectionManager.Instance;
            if (splinterManager == null)
            {
                return; // Splinter system not active
            }
            
            // Use breach's splinter drop configuration instead of enemy's
            var dropConfig = breachMarker.BreachSplinterConfig;
            if (dropConfig == null)
            {
                if (enableEnemyDebugLogging)
                {
                    Debug.LogWarning($"[BaseEnemy] {gameObject.name}: Breach marker found but no drop config assigned");
                }
                return;
            }
            
            // Calculate distance from spawn for scaling
            int distanceFromSpawn = 0;
            var chunkManager = TilemapChunkManager.Instance;
            if (chunkManager != null)
            {
                ChunkCoordinate currentChunk = ChunkCoordinate.FromWorldPosition(transform.position, chunkManager.GetChunkWidth(), chunkManager.GetChunkHeight());
                distanceFromSpawn = Mathf.Abs(currentChunk.x) + Mathf.Abs(currentChunk.y);
            }
            
            // Calculate splinter drop using breach configuration
            var dropResult = dropConfig.CalculateSplinterDrop(enemyType, distanceFromSpawn);
            
            // Filter drop result through breach's allowed splinter types
            var filteredResult = breachMarker.FilterDropResult(dropResult);
            
            if (filteredResult.hasDropped && filteredResult.amount > 0)
            {
                // Spawn splinter pickup using the actual drop configuration (includes prefab)
                SpawnSplinterPickup(filteredResult.selectedDrop, filteredResult.amount);
                
                if (enableEnemyDebugLogging)
                {
                    Debug.Log($"[BaseEnemy] {gameObject.name}: Breach-spawned enemy dropped {filteredResult.amount}x {filteredResult.splinterType}");
                }
            }
            else if (enableEnemyDebugLogging && dropResult.hasDropped && !filteredResult.hasDropped)
            {
                Debug.Log($"[BaseEnemy] {gameObject.name}: Drop {dropResult.splinterType} was filtered out by breach restrictions (allowed: {breachMarker.AllowedSplinterTypes})");
            }
        }
        
        private void SpawnSplinterPickup(SplinterDrop splinterDrop, int amount)
        {
            if (splinterDrop?.prefab == null)
            {
                Debug.LogError($"[BaseEnemy] {gameObject.name}: Cannot spawn splinter pickup - no prefab assigned for {splinterDrop?.splinterType}");
                return;
            }
            
            // Spawn pickup at enemy position with slight random offset
            Vector3 spawnPosition = transform.position + new Vector3(
                Random.Range(-0.5f, 0.5f), 
                Random.Range(-0.5f, 0.5f), 
                0f
            );
            
            // Try to spawn from pool using the configured prefab name
            var poolManager = PoolManager.Instance;
            if (poolManager == null)
            {
                Debug.LogWarning($"[BaseEnemy] {gameObject.name}: PoolManager not available - cannot spawn {splinterDrop.splinterType} pickup");
                return;
            }
            
            // Use the configured prefab name for PoolManager lookup
            string prefabName = splinterDrop.prefab.name;
            var pickupObject = poolManager.Spawn(prefabName, spawnPosition);
            
            if (pickupObject != null)
            {
                var pickup = pickupObject.GetComponent<SplinterPickup>();
                if (pickup != null)
                {
                    pickup.ConfigurePickup(splinterDrop.splinterType, amount, spawnPosition);
                    
                    if (enableEnemyDebugLogging)
                    {
                        Debug.Log($"[BaseEnemy] {gameObject.name}: Spawned {prefabName} pickup with {amount}x {splinterDrop.splinterType} at {spawnPosition}");
                    }
                }
                else
                {
                    Debug.LogError($"[BaseEnemy] Spawned object {prefabName} does not have SplinterPickup component");
                    // Return to pool to avoid leaving objects in scene
                    poolManager.Despawn(pickupObject);
                }
            }
            else
            {
                // Warning: Pool spawning failed - prefab likely not registered in PoolManager
                Debug.LogWarning($"[BaseEnemy] {gameObject.name}: Failed to spawn '{prefabName}' from pool - check if prefab is registered in PoolManager. Lost drop: {amount}x {splinterDrop.splinterType}");
            }
        }
        
        private void NotifyBuffSystemOfKill()
        {
            var buffSystem = PlayerBuffSystem.Instance;
            if (buffSystem != null)
            {
                buffSystem.OnEnemyKilled(gameObject);
            }
        }
        
        /// <summary>
        /// Notify the progress tracker about this enemy kill for unlock progress.
        /// </summary>
        private void NotifyProgressTracker()
        {
            if (ProgressTracker.Instance != null)
            {
                // Get race name from enemy race data if available
                string raceName = GetRaceName();
                
                ProgressTracker.Instance.OnEnemyKilled(enemyType, raceName);
            }
        }
        
        /// <summary>
        /// Get the race name for this enemy if it has race data.
        /// </summary>
        /// <returns>Race name or null if no race data</returns>
        private string GetRaceName()
        {
            // For now, use a simple implementation
            // Can be extended later when race system is more developed
            return null; // Will only track by enemy type for now
        }
        
        
        // ISpawnable implementation
        public virtual void OnSpawn()
        {
            // Reset state
            isDead = false;
            
            // Reset health to full
            if (enemyHealth != null)
            {
                enemyHealth.ResetHealth();
            }
            
            // Re-enable collider
            if (col2D != null)
            {
                col2D.enabled = true;
            }
            
            // Notify movement strategy
            movementStrategy?.OnSpawnFromPool();
            
            // Notify attack strategy
            attackStrategy?.OnSpawnFromPool();
            
            // Reset chunk tracking to force update on first frame
            currentChunk = Vector3Int.one * int.MinValue;
            
            // Register with batch processor if available
            if (ChunkBuffBatchProcessor.Instance != null)
            {
                ChunkBuffBatchProcessor.Instance.RegisterEnemy(this);
            }
            else if (ChunkBuffSystem.Instance != null)
            {
                // Fallback: apply modifiers directly if batch processor is unavailable
                var chunk = ChunkBuffSystem.WorldToChunkPosition(transform.position);
                var modifiers = ChunkBuffSystem.Instance.GetChunkModifiers(chunk);
                UpdateAttackModifiers(modifiers);
                currentChunk = chunk; // Set proper chunk tracking after reset
                
                if (enableEnemyDebugLogging)
                {
                    Debug.Log($"[BaseEnemy] {name}: Using direct modifier application (batch processor unavailable)");
                }
            }
        }
        
        public virtual void OnDespawn()
        {
            // Cleanup movement
            movementStrategy?.OnReturnToPool();
            
            // Cleanup attack strategy
            attackStrategy?.OnReturnToPool();
            
            // Unregister from batch processor
            if (ChunkBuffBatchProcessor.Instance != null)
            {
                ChunkBuffBatchProcessor.Instance.UnregisterEnemy(this);
            }
            
            // Clear any pending chunk modifiers to prevent memory leaks
            pendingChunkModifiers = null;
            
            // Additional cleanup can be added in derived classes
        }
        
        /// <summary>
        /// Updates the current target point based on the selection mode.
        /// </summary>
        protected virtual void UpdateTargetSelection()
        {
            if (PlayerManager.PlayerTransform == null) return;
            
            // Get target points from PlayerManager
            var targetPoints = PlayerManager.TargetPoints;
            
            if (targetPoints.Length == 0)
            {
                // No target points found, use player root
                currentTargetPoint = null;
                return;
            }
            
            // Select target based on mode
            switch (targetSelectionMode)
            {
                case TargetSelectionMode.Nearest:
                    currentTargetPoint = GetNearestTargetPoint(targetPoints);
                    break;
                    
                case TargetSelectionMode.HighestPriority:
                    currentTargetPoint = GetHighestPriorityTargetPoint(targetPoints);
                    break;
                    
                case TargetSelectionMode.PreferType:
                    currentTargetPoint = GetPreferredTypeTargetPoint(targetPoints);
                    break;
                    
                case TargetSelectionMode.Random:
                    currentTargetPoint = targetPoints[Random.Range(0, targetPoints.Length)];
                    break;
                    
                default:
                    currentTargetPoint = targetPoints[0];
                    break;
            }
        }
        
        /// <summary>
        /// Gets the current target position, either from target point or player root.
        /// </summary>
        protected virtual Vector3 GetCurrentTargetPosition()
        {
            if (currentTargetPoint != null)
            {
                return currentTargetPoint.GetTargetPosition();
            }
            
            if (PlayerManager.PlayerTransform != null)
            {
                return PlayerManager.PlayerPosition;
            }
            
            return transform.position;
        }
        
        /// <summary>
        /// Update attack modifiers based on current chunk position.
        /// TODO: Activate once ChunkBuffSystem is compiled
        /// </summary>
        protected virtual void UpdateChunkBuffs()
        {
            // Get current chunk position
            Vector3Int newChunk = ChunkBuffSystem.WorldToChunkPosition(transform.position);
            
            // Only update if chunk changed
            if (newChunk != currentChunk)
            {
                Vector3Int oldChunk = currentChunk;
                currentChunk = newChunk;
                
                // If batch processor is available, let it handle the update
                if (ChunkBuffBatchProcessor.Instance != null)
                {
                    ChunkBuffBatchProcessor.Instance.UpdateEnemyChunk(this, oldChunk, newChunk);
                }
                else if (ChunkBuffSystem.Instance != null)
                {
                    // Fallback to direct update if no batch processor
                    var chunkModifiers = ChunkBuffSystem.Instance.GetChunkModifiers(currentChunk);
                    UpdateAttackModifiers(chunkModifiers);
                }
            }
        }
        
        /// <summary>
        /// Update attack modifiers from external source (called by ChunkBuffBatchProcessor)
        /// </summary>
        public void UpdateAttackModifiers(List<AttackModifier> modifiers)
        {
            if (attackStrategy != null)
            {
                attackStrategy.UpdateModifiers(modifiers);
                
                if (enableEnemyDebugLogging)
                {
                    int projectileModifierCount = 0;
                    for (int i = 0; i < modifiers.Count; i++)
                    {
                        if (modifiers[i].type == AttackModifierType.ProjectileCount)
                            projectileModifierCount++;
                    }
                    Debug.Log($"[BaseEnemy] {name}: Applied {modifiers.Count} chunk modifiers (including {projectileModifierCount} projectile modifiers)");
                }
            }
            else
            {
                // Store modifiers for later application when attack strategy is ready
                pendingChunkModifiers = modifiers;
                
                if (enableEnemyDebugLogging)
                {
                    Debug.LogWarning($"[BaseEnemy] {name}: Attack strategy not ready - storing {modifiers.Count} modifiers for later application");
                }
            }
        }
        
        /// <summary>
        /// Gets the nearest target point to this enemy.
        /// </summary>
        protected PlayerTargetPoint GetNearestTargetPoint(PlayerTargetPoint[] targetPoints)
        {
            PlayerTargetPoint nearest = null;
            float nearestDistance = float.MaxValue;
            
            foreach (var point in targetPoints)
            {
                float distance = Vector3.Distance(transform.position, point.GetTargetPosition());
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearest = point;
                }
            }
            
            return nearest;
        }
        
        /// <summary>
        /// Gets the target point with the highest priority.
        /// </summary>
        protected PlayerTargetPoint GetHighestPriorityTargetPoint(PlayerTargetPoint[] targetPoints)
        {
            if (targetPoints == null || targetPoints.Length == 0) return null;
            
            PlayerTargetPoint highest = targetPoints[0];
            int highestPriority = highest.Priority;
            
            for (int i = 1; i < targetPoints.Length; i++)
            {
                if (targetPoints[i].Priority > highestPriority)
                {
                    highest = targetPoints[i];
                    highestPriority = targetPoints[i].Priority;
                }
            }
            
            return highest;
        }
        
        /// <summary>
        /// Gets a target point matching preferred types, or falls back to highest priority.
        /// </summary>
        protected PlayerTargetPoint GetPreferredTypeTargetPoint(PlayerTargetPoint[] targetPoints)
        {
            if (targetPoints == null || targetPoints.Length == 0) return null;
            
            // First try to find a matching type with highest priority
            foreach (var preferredType in preferredTargetTypes)
            {
                PlayerTargetPoint bestMatch = null;
                int bestPriority = -1;
                
                // Find highest priority point of this type
                for (int i = 0; i < targetPoints.Length; i++)
                {
                    var point = targetPoints[i];
                    if (point.TargetType == preferredType && point.Priority > bestPriority)
                    {
                        bestMatch = point;
                        bestPriority = point.Priority;
                    }
                }
                
                if (bestMatch != null)
                {
                    return bestMatch;
                }
            }
            
            // No preferred type found, use highest priority
            return GetHighestPriorityTargetPoint(targetPoints);
        }
        
        /// <summary>
        /// Draws debug visualization for target selection.
        /// </summary>
        protected virtual void DrawTargetDebug()
        {
            if (PlayerManager.PlayerTransform == null) return;
            
            Vector3 targetPos = GetCurrentTargetPosition();
            
            // Draw line to target
            UnityEngine.Debug.DrawLine(transform.position, targetPos, Color.red);
            
            // Draw sphere at target position
            UnityEngine.Debug.DrawLine(targetPos + Vector3.left * 0.2f, targetPos + Vector3.right * 0.2f, Color.red);
            UnityEngine.Debug.DrawLine(targetPos + Vector3.up * 0.2f, targetPos + Vector3.down * 0.2f, Color.red);
        }
        
        protected virtual void OnDestroy()
        {
            if (enemyHealth != null)
            {
                enemyHealth.OnDeath.RemoveListener(HandleDeath);
            }
            
            // Unsubscribe from movement events
            if (movementStrategy != null)
            {
                movementStrategy.OnAttackRequested -= ExecuteAttack;
            }
        }
        
        // Debug helpers
        [Title("Debug")]
        [Button("Kill Enemy")]
        protected void DebugKill()
        {
            if (Application.isPlaying && enemyHealth != null)
            {
                // Create damage info for debug kill
                var damageInfo = DamageInfo.FromSingleType(
                    enemyHealth.MaxHealth,
                    DamageType.Physical,
                    false, // Not a critical hit
                    1f,    // No crit multiplier
                    "DebugKill"
                );
                enemyHealth.TakeDamage(damageInfo);
            }
        }
        
        [Button("Log Current Target")]
        protected void DebugLogTarget()
        {
            if (Application.isPlaying)
            {
                if (enableEnemyDebugLogging)
                {
                    if (currentTargetPoint != null)
                    {
                        UnityEngine.Debug.Log($"Current target: {currentTargetPoint.TargetType} at {currentTargetPoint.GetTargetPosition()}");
                    }
                    else if (PlayerManager.PlayerTransform != null)
                    {
                        UnityEngine.Debug.Log($"Current target: Player root at {PlayerManager.PlayerPosition}");
                    }
                    else
                    {
                        UnityEngine.Debug.Log("No target found");
                    }
                }
            }
        }
        
        [Button("Log Attack Strategy")]
        protected void DebugLogAttackStrategy()
        {
            if (Application.isPlaying)
            {
                if (attackStrategy != null)
                {
                    UnityEngine.Debug.Log($"Attack Strategy: {attackStrategy.GetType().Name}" +
                              $"\n- Damage: {attackStrategy.Damage}" +
                              $"\n- Range: {attackStrategy.AttackRange}" +
                              $"\n- Cooldown: {attackStrategy.AttackCooldown}" +
                              $"\n- Animation: {attackStrategy.AttackAnimationName}");
                }
                else
                {
                    UnityEngine.Debug.Log("No attack strategy assigned");
                }
            }
        }
        
        [Button("Show Chunk Buff Effects")]
        protected void DebugShowChunkBuffEffects()
        {
            if (Application.isPlaying && attackStrategy != null)
            {
                // Get base values from attack strategy
                float baseDamage = attackStrategy.Damage;
                float baseRange = attackStrategy.AttackRange;
                float baseCooldown = attackStrategy.AttackCooldown;
                
                // Get current chunk info
                Vector3Int chunk = ChunkBuffSystem.WorldToChunkPosition(transform.position);
                
                string debugInfo = $"=== CHUNK BUFF ANALYSIS for {gameObject.name} ===\n";
                debugInfo += $"Current Chunk: {chunk}\n";
                debugInfo += $"World Position: {transform.position}\n\n";
                
                // Show base stats
                debugInfo += "BASE STATS:\n";
                debugInfo += $"  Base Damage: {baseDamage}\n";
                debugInfo += $"  Base Range: {baseRange:F1}\n";
                debugInfo += $"  Base Cooldown: {baseCooldown:F1}s\n\n";
                
                // Show modified stats (if modifiers exist)
                if (ChunkBuffSystem.Instance != null)
                {
                    var chunkModifiers = ChunkBuffSystem.Instance.GetChunkModifiers(chunk);
                    
                    debugInfo += $"CHUNK MODIFIERS ({chunkModifiers.Count} active):\n";
                    if (chunkModifiers.Count > 0)
                    {
                        foreach (var modifier in chunkModifiers)
                        {
                            debugInfo += $"  {modifier.type}: +{modifier.value} (from {modifier.source})\n";
                        }
                        
                        debugInfo += "\nMODIFIED STATS:\n";
                        debugInfo += $"  Current Damage: {attackStrategy.Damage} → ";
                        
                        // Calculate what the damage would be with crit
                        if (attackStrategy is RangedAttackStrategy ranged)
                        {
                            var critResult = ranged.Modifiers.CalculateDamage(attackStrategy.Damage);
                            debugInfo += $"Up to {critResult.finalDamage} with crit\n";
                            debugInfo += $"  Projectile Count: {ranged.Modifiers.GetProjectileCount()}\n";
                            debugInfo += $"  Projectile Speed: {ranged.Modifiers.GetModifiedProjectileSpeed(ranged.ProjectileSpeed):F1}\n";
                        }
                        else if (attackStrategy is MeleeAttackStrategy melee)
                        {
                            var critResult = melee.Modifiers.CalculateDamage(attackStrategy.Damage);
                            debugInfo += $"Up to {critResult.finalDamage} with crit\n";
                        }
                        
                        debugInfo += $"  Current Range: {attackStrategy.AttackRange:F1} (base: {baseRange:F1})\n";
                        debugInfo += $"  Current Cooldown: {attackStrategy.AttackCooldown:F1}s (base: {baseCooldown:F1}s)\n";
                        
                        // Show crit stats
                        if (attackStrategy is RangedAttackStrategy rangedStrat)
                        {
                            var modifiers = rangedStrat.Modifiers;
                            debugInfo += $"\nCRIT STATS:\n";
                            debugInfo += $"  Base Crit Chance: 5%\n";
                            debugInfo += $"  Base Crit Damage: 150%\n";
                            debugInfo += $"  Effective Crit Chance: ~{(0.05f + modifiers.GetModifiers(AttackModifierType.CritChance).Sum(m => m.value) / 100f) * 100f:F1}%\n";
                            debugInfo += $"  Effective Crit Damage: ~{(1.5f + modifiers.GetModifiers(AttackModifierType.CritDamage).Sum(m => m.value) / 100f) * 100f:F0}%\n";
                        }
                        else if (attackStrategy is MeleeAttackStrategy meleeStrat)
                        {
                            var modifiers = meleeStrat.Modifiers;
                            debugInfo += $"\nCRIT STATS:\n";
                            debugInfo += $"  Base Crit Chance: 5%\n";
                            debugInfo += $"  Base Crit Damage: 150%\n";
                            debugInfo += $"  Effective Crit Chance: ~{(0.05f + modifiers.GetModifiers(AttackModifierType.CritChance).Sum(m => m.value) / 100f) * 100f:F1}%\n";
                            debugInfo += $"  Effective Crit Damage: ~{(1.5f + modifiers.GetModifiers(AttackModifierType.CritDamage).Sum(m => m.value) / 100f) * 100f:F0}%\n";
                        }
                    }
                    else
                    {
                        debugInfo += "  No modifiers active\n";
                        debugInfo += "\nCurrent stats same as base stats.\n";
                    }
                }
                else
                {
                    debugInfo += "ChunkBuffSystem not found!\n";
                }
                
                UnityEngine.Debug.Log($"<color=orange>{debugInfo}</color>");
            }
        }
    }