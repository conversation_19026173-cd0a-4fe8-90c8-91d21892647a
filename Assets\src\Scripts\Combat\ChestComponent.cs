
/*
using UnityEngine;
using Sirenix.OdinInspector;
using UnityEngine.Events;
using System.Collections;

/// <summary>
/// Interactive chest that can be opened by the player to receive loot
/// Requires SpatialCollider configured as:
/// - Shape: Circle
/// - Radius: Match interactionRange
/// - Layer: Interactable
/// - IsTrigger: true

[RequireComponent(typeof(SpriteRenderer))]
[RequireComponent(typeof(SpriteAnimator))]
public class ChestComponent : MonoBehaviour
{
    public enum ChestState
    {
        Closed,
        Opening,
        Opened
    }
    
    [Title("Chest Configuration")]
    [SerializeField]
    [ReadOnly]
    private ChestState currentState = ChestState.Closed;
    
    [SerializeField]
    [MinValue(0.1f)]
    [Tooltip("Range at which player can interact with chest")]
    private float interactionRange = 1.5f;
    
    [SerializeField]
    [Tooltip("Can this chest be opened multiple times?")]
    private bool isReusable = false;
    
    [Title("Animation References")]
    [SerializeField]
    [Required("Closed animation is required")]
    [AssetsOnly]
    [Tooltip("Animation to play when chest is closed")]
    private SpriteAnimation closedAnimation;
    
    [SerializeField]
    [Required("Opening animation is required")]
    [AssetsOnly]
    [Tooltip("Animation to play when chest is opening")]
    private SpriteAnimation openingAnimation;
    
    [SerializeField]
    [Required("Opened animation is required")]
    [AssetsOnly]
    [Tooltip("Animation to play when chest is opened")]
    private SpriteAnimation openedAnimation;    
    [Title("Interaction")]
    [SerializeField]
    [InfoBox("Requires player to press interaction key (E) when in range")]
    private bool requiresInteraction = true;
    
    [SerializeField]
    [ShowIf("requiresInteraction")]
    private KeyCode interactionKey = KeyCode.E;
    
    [Title("Visual Feedback")]
    [SerializeField]
    private GameObject interactionPrompt;
    
    [SerializeField]
    [MinValue(0f)]
    private float promptYOffset = 1.5f;
    
    [Title("Audio")]
    [SerializeField]
    [AssetsOnly]
    private AudioClip openSound;
    
    [SerializeField]
    [AssetsOnly]
    private AudioClip lockedSound;
    
    [Title("Currency Drops")]
    [SerializeField]
    [Tooltip("Configuration for currency drops from this chest")]
    private CurrencyConfig currencyConfig;
    
    [SerializeField]
    [Tooltip("Enable currency drops when chest is opened")]
    private bool dropsCurrency = true;
    
    [SerializeField]
    [ShowIf("dropsCurrency")]
    [Range(0.1f, 2f)]
    [Tooltip("Delay before currency starts dropping (allows animation to complete)")]
    private float currencyDropDelay = 0.2f;
    
    [Title("Events")]
    public UnityEvent OnChestOpened;
    public UnityEvent OnChestClosed;
    
    [Title("Debug")]
    [SerializeField]
    private bool showDebugInfo = false;
    
    // Components

    private SpriteRenderer spriteRenderer;
    private SpriteAnimator spriteAnimator;
    
    // Runtime
    private bool playerInRange = false;
    private Transform playerTransform;
    private float openingStartTime;
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private bool PlayerInRange => playerInRange;
    
    private void Awake()
    {
        // Cache components
    
        spriteRenderer = GetComponent<SpriteRenderer>();
        spriteAnimator = GetComponent<SpriteAnimator>();
        
 
        // Note: shape, radius and isTrigger need to be set in the inspector
        
        // Subscribe to animation events
        if (spriteAnimator != null)
        {
        spriteAnimator.OnAnimationCompleted += OnAnimationComplete;
        }
        
        // Setup interaction prompt
        if (interactionPrompt != null)
        {
        interactionPrompt.SetActive(false);
        
        // Position prompt above chest
        interactionPrompt.transform.localPosition = new Vector3(0, promptYOffset, 0);
        }
    }
    
    private void Start()
    {
        // Play initial animation
        if (closedAnimation != null)
        {
        spriteAnimator.Play(closedAnimation);
        }
    }    
    private void Update()
    {
        if (currentState == ChestState.Closed && playerInRange && requiresInteraction)
        {
        // Check for interaction input
        if (Input.GetKeyDown(interactionKey))
        {
            AttemptOpen();
        }
        }
    }
    
    private void OnDestroy()
    {
        if (spriteAnimator != null)
        {
        spriteAnimator.OnAnimationCompleted -= OnAnimationComplete;
        }
    }
    
    // ISpatialCollisionHandler implementation
    public void HandleCollisionEnter(CollisionInfo collision) { }
    public void HandleCollisionStay(CollisionInfo collision) { }
    public void HandleCollisionExit(CollisionInfo collision) { }
    
    public void HandleTriggerEnter(CollisionInfo collision)
    {
        if (collision.Other.Layer.HasFlag(CollisionLayers.Player))
        {
        playerInRange = true;
        playerTransform = collision.Other.GameObject.transform;
        
        // Show interaction prompt if chest is closed
        if (currentState == ChestState.Closed && interactionPrompt != null && requiresInteraction)
        {
            interactionPrompt.SetActive(true);
        }
        
        // Auto-open if no interaction required
        if (!requiresInteraction && currentState == ChestState.Closed)
        {
            AttemptOpen();
        }
        
        if (showDebugInfo)
        {
            Debug.Log("ChestComponent: Player entered interaction range");
        }
        }
    }
    
    public void HandleTriggerStay(CollisionInfo collision) { }
    
    public void HandleTriggerExit(CollisionInfo collision)
    {
        if (collision.Other.Layer.HasFlag(CollisionLayers.Player))
        {
        playerInRange = false;
        playerTransform = null;
        
        // Hide interaction prompt
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(false);
        }
        
        if (showDebugInfo)
        {
            Debug.Log("ChestComponent: Player left interaction range");
        }
        }
    }
    
    private void AttemptOpen()
    {
        if (currentState != ChestState.Closed)
        return;
        
        // TODO: Check if player has required key or conditions
        bool canOpen = true;
        
        if (canOpen)
        {
        Open();
        }
        else
        {
        // Play locked sound
        if (lockedSound != null)
        {
            AudioSource.PlayClipAtPoint(lockedSound, transform.position);
        }
        
        if (showDebugInfo)
        {
            Debug.Log("ChestComponent: Chest is locked!");
        }
        }
    }
    
    private void Open()
    {
        if (currentState != ChestState.Closed)
        return;
        
        currentState = ChestState.Opening;
        openingStartTime = Time.time;
        
        // Hide interaction prompt
        if (interactionPrompt != null)
        {
        interactionPrompt.SetActive(false);
        }
        
        // Play opening animation
        if (openingAnimation != null)
        {
        spriteAnimator.Play(openingAnimation);
        }
        else
        {
        // If no opening animation, go straight to opened
        OnOpeningComplete();
        }        
        // Play open sound
        if (openSound != null)
        {
        AudioSource.PlayClipAtPoint(openSound, transform.position);
        }
        
        if (showDebugInfo)
        {
        Debug.Log("ChestComponent: Opening chest...");
        }
    }
    
    private void OnAnimationComplete(string animationName)
    {
        if (openingAnimation != null && animationName == openingAnimation.AnimationName)
        {
        OnOpeningComplete();
        }
    }    
    private void OnOpeningComplete()
    {
        currentState = ChestState.Opened;
        
        // Play opened animation
        if (openedAnimation != null)
        {
        spriteAnimator.Play(openedAnimation);
        }
        
        // Fire event
        OnChestOpened?.Invoke();
        
        // Spawn currency after a delay
        if (dropsCurrency && currencyConfig != null)
        {
            StartCoroutine(SpawnCurrencyDelayed());
        }
        
        if (showDebugInfo)
        {
        Debug.Log("ChestComponent: Chest opened!");
        }
        
        // If not reusable, disable further interaction
        if (!isReusable)
        {
        spatialCollider.enabled = false;
        }
    }
    
    public void ResetChest()
    {
        if (!isReusable)
        {
        Debug.LogWarning("ChestComponent: Cannot reset non-reusable chest!");
        return;
        }
        
        currentState = ChestState.Closed;
        
        // Re-enable collider
        spatialCollider.enabled = true;
        
        // Play closed animation
        if (closedAnimation != null)
        {
        spriteAnimator.Play(closedAnimation);
        }        
        // Fire event
        OnChestClosed?.Invoke();
        
        if (showDebugInfo)
        {
        Debug.Log("ChestComponent: Chest reset to closed state");
        }
    }
    
    #region Currency Drops
    
    /// <summary>
    /// Spawn currency with a delay to allow chest animation to complete
    /// </summary>
    private IEnumerator SpawnCurrencyDelayed()
    {
        yield return new WaitForSeconds(currencyDropDelay);
        SpawnCurrency();
    }
    
    /// <summary>
    /// Spawn currency pickups around the chest
    /// </summary>
    private void SpawnCurrency()
    {
        if (currencyConfig == null || !dropsCurrency)
        {
            if (showDebugInfo)
            {
                Debug.LogWarning("ChestComponent: Cannot spawn currency - missing config or drops disabled");
            }
            return;
        }
        
        if (currencyConfig.currencyPickupPrefab == null)
        {
            Debug.LogError("ChestComponent: Currency pickup prefab is missing from config!");
            return;
        }
        
        // Get random drop amount
        int dropAmount = currencyConfig.GetRandomChestDropAmount();
        
        if (showDebugInfo)
        {
            Debug.Log($"ChestComponent: Spawning {dropAmount} currency pickups");
        }
        
        // Spawn currency pickups scattered around chest
        for (int i = 0; i < dropAmount; i++)
        {
            Vector3 spawnPosition = currencyConfig.GetChestScatterPosition(transform.position);
            SpawnSingleCurrency(spawnPosition, currencyConfig.baseCurrencyValue);
        }
        
        // Play drop sound if configured
        if (currencyConfig.dropSound != null)
        {
            AudioSource.PlayClipAtPoint(currencyConfig.dropSound, transform.position, currencyConfig.audioVolume);
        }
    }
    
    /// <summary>
    /// Spawn a single currency pickup at the specified position
    /// </summary>
    /// <param name="position">Position to spawn the currency</param>
    /// <param name="value">Currency value</param>
    private void SpawnSingleCurrency(Vector3 position, int value)
    {
        if (PoolManager.Instance == null)
        {
            Debug.LogError("ChestComponent: PoolManager instance not found!");
            return;
        }
        
        // Spawn from pool
        GameObject currencyObj = PoolManager.Instance.Spawn(
            currencyConfig.currencyPickupPrefab,
            position,
            Quaternion.identity
        );
        
        if (currencyObj == null)
        {
            Debug.LogError("ChestComponent: Failed to spawn currency pickup from pool");
            return;
        }
        
        // Configure the pickup
        if (PoolManager.Instance.GetCachedComponent<CurrencyPickup>(currencyObj, out var pickup))
        {
            pickup.ConfigurePickup(
                currencyConfig.defaultAttractionRange,
                currencyConfig.defaultMoveSpeed,
                value
            );
        }
        else
        {
            Debug.LogError("ChestComponent: Spawned object does not have CurrencyPickup component!");
        }
    }
    
    #endregion
    
    // Editor helpers
    [Button("Open Chest"), ShowIf("@UnityEngine.Application.isPlaying && currentState == ChestState.Closed")]
    private void EditorOpen()
    {
        Open();
    }
    
    [Button("Reset Chest"), ShowIf("@UnityEngine.Application.isPlaying && currentState == ChestState.Opened && isReusable")]
    private void EditorReset()
    {
        ResetChest();
    }
    
    private void OnDrawGizmosSelected()
    {
        // Draw interaction range
        Gizmos.color = playerInRange ? Color.green : Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionRange);
        
        // Draw prompt position
        if (interactionPrompt != null)
        {
        Gizmos.color = Color.cyan;
        Vector3 promptPos = transform.position + Vector3.up * promptYOffset;
        Gizmos.DrawWireCube(promptPos, Vector3.one * 0.5f);
    }
    }
}*/