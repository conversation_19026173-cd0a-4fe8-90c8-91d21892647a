using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Area damage system using Unity Physics2D with optimized hit processing modes
/// Supports zero-GC area damage detection with pre-allocated arrays
/// </summary>
public class AreaDamageSystem : MonoBehaviour, ISpawnable
{
    [Title("Area Damage Settings")]
    [SerializeField, Tooltip("FirstHitOnly: Single target ability (rare for area effects)\nAllHits: Process all collisions (typical for area effects)\nMaxHits: Process up to max limit (controlled multi-hit)")]
    private HitProcessingMode hitProcessingMode = HitProcessingMode.AllHits;
    
    [SerializeField, Range(1, 50), ShowIf("hitProcessingMode", HitProcessingMode.MaxHits)]
    [Tooltip("Max targets for MaxHits mode")]
    private int maxTargets = 10;
    
    [SerializeField, Range(0.5f, 15f), Tooltip("Default damage radius")]
    private float defaultRadius = 5f;
    
    [Title("Target Filtering")]
    [SerializeField, Tooltip("Which layers this area effect can damage")]
    private LayerMask defaultTargetMask = -1;
    
    [SerializeF<PERSON>, Too<PERSON><PERSON>("Include trigger colliders in area damage")]
    private bool includeTriggers = false;
    
    [Title("Damage Scaling")]
    [SerializeField, Tooltip("Damage falloff with distance from center")]
    private AnimationCurve damageFalloff = AnimationCurve.Linear(0, 1, 1, 0.3f);
    
    [Title("Debug")]
    [SerializeField, Tooltip("Show area damage debug info in scene view")]
    private bool showAreaDebug = false;
    
    [SerializeField, ShowIf("showAreaDebug"), Tooltip("Debug gizmo color")]
    private Color debugColor = Color.red;
    
    // Pre-allocated arrays for zero-GC collision detection
    private static readonly Collider2D[] areaResults = new Collider2D[50];
    private ContactFilter2D targetFilter;
    private LayerMask originalTargetMask;
    
    // State variables requiring pool reset
    private readonly System.Collections.Generic.Dictionary<int, float> lastHitTimes = new System.Collections.Generic.Dictionary<int, float>();
    private readonly System.Collections.Generic.List<Transform> activeTargets = new System.Collections.Generic.List<Transform>();
    private bool isEffectActive = false;
    private float effectStartTime = 0f;
    private int totalHitsProcessed = 0;
    
    void Start()
    {
        // Initialize contact filter
        targetFilter = new ContactFilter2D();
        targetFilter.SetLayerMask(defaultTargetMask);
        targetFilter.useTriggers = includeTriggers;
        originalTargetMask = defaultTargetMask;
    }
    
    /// <summary>
    /// Apply area damage at specified location with custom parameters
    /// </summary>
    public void ApplyAreaDamage(Vector2 center, float radius, float damage, LayerMask targetMask)
    {
        if (!isEffectActive) return; // Skip if not properly spawned
        
        // Update target filter for this specific damage call
        targetFilter.SetLayerMask(targetMask);
        
        // Use RaycastCollisionHelper.OverlapCircleNonAlloc for zero-GC detection
        int hits = RaycastCollisionHelper.OverlapCircleNonAlloc(center, radius, areaResults, targetFilter.layerMask);
        
        ProcessAreaHits(hits, center, radius, damage);
    }
    
    /// <summary>
    /// Apply area damage at current position with default settings
    /// </summary>
    public void ApplyAreaDamage(float damage)
    {
        ApplyAreaDamage(transform.position, defaultRadius, damage, defaultTargetMask);
    }
    
    /// <summary>
    /// Process area damage hits based on hit processing mode
    /// </summary>
    private void ProcessAreaHits(int hitCount, Vector2 center, float radius, float damage)
    {
        if (hitCount == 0) return;
        
        if (hitProcessingMode == HitProcessingMode.FirstHitOnly)
        {
            // Single target ability (rare for area effects)
            ProcessSingleHit(areaResults[0], center, radius, damage);
            return; // Early exit - performance boost
        }
        else if (hitProcessingMode == HitProcessingMode.MaxHits)
        {
            // Limited multi-hit (controlled area damage)
            int processCount = Mathf.Min(hitCount, maxTargets);
            for (int i = 0; i < processCount; i++)
            {
                ProcessSingleHit(areaResults[i], center, radius, damage);
            }
        }
        else // AllHits mode - typical for area effects
        {
            // Process ALL targets in area (explosive damage)
            for(int i = 0; i < hitCount; i++)
            {
                ProcessSingleHit(areaResults[i], center, radius, damage);
            }
        }
    }
    
    /// <summary>
    /// Process damage for a single target with distance falloff
    /// </summary>
    private void ProcessSingleHit(Collider2D collider, Vector2 center, float radius, float damage)
    {
        if (collider == null) return;
        
        int targetID = collider.GetInstanceID();
        float currentTime = Time.time;
        
        // Track hit timing to prevent spam (0.1s minimum between hits on same target)
        if (lastHitTimes.ContainsKey(targetID) && 
            currentTime - lastHitTimes[targetID] < 0.1f) return;
        
        lastHitTimes[targetID] = currentTime;
        activeTargets.Add(collider.transform);
        totalHitsProcessed++;
        
        // Calculate distance-based damage falloff
        float distance = Vector2.Distance(center, collider.transform.position);
        float normalizedDistance = Mathf.Clamp01(distance / radius);
        float damageMultiplier = damageFalloff.Evaluate(normalizedDistance);
        float finalDamage = damage * damageMultiplier;
        
        // Apply damage based on target type
        GameObject target = collider.gameObject;
        
        // Handle player damage via PlayerManager
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
            DamageInfo damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                DamageType.Physical, // Default to physical for area effects
                false, // Area effects typically don't crit
                1f,
                "AreaDamage"
            );
            
            PlayerManager.DealDamageToPlayer(damageInfo);
            
            if (showAreaDebug)
            {
                Debug.Log($"AreaDamage: Hit player for {finalDamage:F1} damage (distance: {distance:F1}, multiplier: {damageMultiplier:F2})");
            }
        }
        // Handle enemy damage via PoolManager - check CombatantHealth first for GC-free lookups
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
        {
            DamageInfo damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                DamageType.Physical,
                false,
                1f,
                "AreaDamage"
            );
            
            combatantHealth.TakeDamage(damageInfo);
            
            if (showAreaDebug)
            {
                Debug.Log($"AreaDamage: Hit {target.name} for {finalDamage:F1} damage (distance: {distance:F1}, multiplier: {damageMultiplier:F2})");
            }
        }
        // Fall back to HealthComponent via PoolManager
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
            DamageInfo damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                DamageType.Physical,
                false,
                1f,
                "AreaDamage"
            );
            
            healthComponent.TakeDamage(damageInfo);
            
            if (showAreaDebug)
            {
                Debug.Log($"AreaDamage: Hit {target.name} for {finalDamage:F1} damage (distance: {distance:F1}, multiplier: {damageMultiplier:F2})");
            }
        }
    }
    
    /// <summary>
    /// Get current effect statistics
    /// </summary>
    public int GetTotalHitsProcessed() => totalHitsProcessed;
    public int GetActiveTargetCount() => activeTargets.Count;
    public bool IsEffectActive => isEffectActive;
    
    // ISpawnable implementation with complete state reset
    public void OnSpawn()
    {
        isEffectActive = true;
        effectStartTime = Time.time;
        totalHitsProcessed = 0;
        // Collections already cleared in OnDespawn
    }
    
    public void OnDespawn()
    {
        // CRITICAL: Complete state reset for pool reuse
        isEffectActive = false;
        effectStartTime = 0f;
        totalHitsProcessed = 0;
        
        // Clear all collections - prevents memory buildup over time
        lastHitTimes.Clear();
        activeTargets.Clear();
        
        // Clear pre-allocated arrays using Array.Clear for performance
        System.Array.Clear(areaResults, 0, areaResults.Length);
        
        // Reset ContactFilter2D if modified at runtime
        if (targetFilter.layerMask != originalTargetMask)
        {
            targetFilter.SetLayerMask(originalTargetMask);
        }
        
        // Reset inspector values if modified at runtime
        if (hitProcessingMode != HitProcessingMode.AllHits)
        {
            hitProcessingMode = HitProcessingMode.AllHits;
        }
    }
    
    // Debug visualization
    void OnDrawGizmosSelected()
    {
        if (!showAreaDebug) return;
        
        // Main area radius
        Gizmos.color = debugColor;
        Gizmos.DrawWireSphere(transform.position, defaultRadius);
        
        // Hit processing mode indicator
        switch (hitProcessingMode)
        {
            case HitProcessingMode.FirstHitOnly:
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(transform.position + Vector3.up * (defaultRadius + 0.5f), Vector3.one * 0.3f);
                break;
                
            case HitProcessingMode.AllHits:
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position + Vector3.up * (defaultRadius + 0.5f), 0.2f);
                break;
                
            case HitProcessingMode.MaxHits:
                Gizmos.color = Color.blue;
                // Draw max hits indicator
                for (int i = 0; i < Mathf.Min(maxTargets, 10); i++)
                {
                    Vector3 pos = transform.position + Vector3.up * (defaultRadius + 0.5f) + 
                                 Vector3.right * (i * 0.2f - maxTargets * 0.1f);
                    Gizmos.DrawWireCube(pos, Vector3.one * 0.1f);
                }
                break;
        }
        
        // Show damage falloff curve as concentric circles
        if (damageFalloff != null)
        {
            Gizmos.color = new Color(debugColor.r, debugColor.g, debugColor.b, 0.3f);
            for (float i = 0.25f; i <= 1f; i += 0.25f)
            {
                float radius = defaultRadius * i;
                float alpha = damageFalloff.Evaluate(i);
                Gizmos.color = new Color(debugColor.r, debugColor.g, debugColor.b, alpha * 0.3f);
                Gizmos.DrawSphere(transform.position, radius);
            }
        }
    }
    
    void OnDrawGizmos()
    {
        if (!showAreaDebug) return;
        
        // Faded area effect
        Gizmos.color = new Color(debugColor.r, debugColor.g, debugColor.b, 0.1f);
        Gizmos.DrawSphere(transform.position, defaultRadius);
    }
    
    // Runtime debug info in Game view
    void OnGUI()
    {
        if (!showAreaDebug || !Application.isPlaying) return;
        
        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position);
        if (screenPos.z > 0) // Only show if in front of camera
        {
            GUI.Label(new Rect(screenPos.x, Screen.height - screenPos.y, 250, 80), 
                $"Mode: {hitProcessingMode}\nRadius: {defaultRadius:F1}\nTargets: {(hitProcessingMode == HitProcessingMode.MaxHits ? maxTargets.ToString() : "∞")}\nHits: {totalHitsProcessed}");
        }
    }
}