using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
using Sirenix.OdinInspector;
using System.Collections;
using System.Linq;

/// <summary>
/// Main skill execution system using strategy pattern for extensible skill type handling.
/// Manages manual and autonomous skill casting with comprehensive support gem integration.
/// Features zero-GC performance, comprehensive caching, and spell echo mechanics.
/// 
/// Key Features:
/// - Strategy-based skill execution for extensibility
/// - Autonomous casting with enemy detection and targeting
/// - Support gem effects: Pierce, Chain, Fork, Area Damage, Spell Echo, Multiple Projectiles
/// - Zero-allocation performance with extensive caching
/// - Spell echo with different targeting for manual vs autonomous casts
/// </summary>
public class SkillExecutor : MonoBehaviour
{
    private SkillExecutorFactory _strategyFactory;
    
    // Bounded cache management constants
    private const int MAX_CACHE_SIZE = 64;
    private const int MAX_SUPPORT_GEMS_CACHE_SIZE = 32;
    private const int MAX_AUTONOMOUS_CACHE_SIZE = 16;

    [Title("References")]
    [SerializeField]
    private EquipmentPanel equipmentPanel;
    
    [SerializeField]
    private PlayerStats playerStats;
    
    [Title("Skill State")]
    [ShowInInspector, ReadOnly]
    private Dictionary<int, float> skillCooldowns = new Dictionary<int, float>();
    
    [ShowInInspector, ReadOnly]
    private Dictionary<int, GemSocketController> cachedControllers = new Dictionary<int, GemSocketController>();
    
    [Title("Spell Echo State")]
    [ShowInInspector, ReadOnly]
    private List<Coroutine> activeSpellEchoes = new List<Coroutine>();

    [Title("Autonomous Skills State")]
    [ShowInInspector, ReadOnly]
    private List<AutonomousSkillData> activeAutonomousSkills = new List<AutonomousSkillData>();
    
    [ShowInInspector, ReadOnly]
    private List<OptimizedAutonomousSkill> optimizedAutonomousSkills = new List<OptimizedAutonomousSkill>();

    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    [Tooltip("Enable debug logging for skill execution events")]

    [SerializeField] private bool enableDamageLogging = false;
    [Tooltip("When enabled, logs final damage values and types for all skill executions")]

    // Pooled collection to reduce GC pressure in Update()
    private readonly List<int> _reusableKeysList = new List<int>();

    // Cached removal indices to eliminate RemoveAll() lambda allocations
    private readonly List<int> _removalIndices = new List<int>();

    // Cached structures for zero-GC cooldown updates
    private readonly Dictionary<int, float> _cooldownUpdates = new Dictionary<int, float>();


    // Cached random vectors pool to avoid Random.insideUnitCircle allocations
    private static readonly Vector2[] _randomVectorPool = new Vector2[32];
    private static bool _randomVectorPoolInitialized = false;
    private int _randomVectorIndex = 0;

    // Object pooling for autonomous skill data to reduce GC pressure
    private readonly Stack<AutonomousSkillData> _autonomousSkillPool = new Stack<AutonomousSkillData>();
    private readonly Stack<OptimizedAutonomousSkill> _optimizedSkillPool = new Stack<OptimizedAutonomousSkill>();

    // Cached vectors to eliminate frequent allocations
    private Vector2 _cachedMouseScreenPos;
    private Vector3 _cachedMouseWorldPos;
    internal Vector3 _cachedVector3Temp;
    internal Vector2 _cachedVector2Temp;
    internal Vector2 _cachedDirectionVector;
    internal Vector2 _cachedOffsetVector;
    internal Vector2 _cachedPerpendicularVector;

    // Cached support gem collections to reduce method call overhead
    private readonly Dictionary<int, List<GemInstance>> _cachedSupportGems = new Dictionary<int, List<GemInstance>>();

    // Cache for nearby enemies to eliminate allocations in enemy detection
    private readonly List<ICollidable> _nearbyEnemiesCache = new List<ICollidable>(50);

    // Cached autonomous support status to avoid repeated detection calls
    private readonly Dictionary<int, bool> _cachedAutonomousStatus = new Dictionary<int, bool>();
    private readonly Dictionary<int, float> _cachedAutonomousRange = new Dictionary<int, float>();
    private readonly Dictionary<int, float> _cachedAutonomousInterval = new Dictionary<int, float>();

    // Shared enemy detection system to eliminate O(N²) spatial queries
    private struct SharedEnemyDetection
    {
        public Vector2 playerPosition;
        public List<ICollidable> allEnemiesInMaxRange;
        public float maxRange;
        public int lastUpdateFrame;
    }
    
    private SharedEnemyDetection _sharedEnemyDetection;


    // Mouse position caching to avoid repeated ScreenToWorldPoint calls
    private Vector3 _lastCachedMousePosition;
    private int _mousePositionCacheFrame = -1;

    // Time caching to avoid multiple Time.time calls per frame
    private float _cachedFrameTime;
    private float _lastAutonomousScanTime; // Timestamp of last equipment scan for autonomous skills

    // Execution-scoped cache to eliminate repeated GemSocketController calculations
    internal struct SkillExecutionCache
    {
        public float finalDamage;
        public float finalCritChance;
        public float finalCritMultiplier;
        public bool hasPierce;
        public bool hasChain;
        public bool hasFork;
        public bool hasAreaDamage;
        public int chainCount;
        public int forkCount;
        public float forkAngle;
        public float areaRadius;
        public float playerModifiedDamage;
        public bool isValid;
        public int slotIndex;
        
        // New damage breakdown fields
        public DamageBreakdown damageBreakdown;
        public DamageType originalDamageType;
        
        // Projectile effects from support gems
        public int totalProjectileCount;
        public float projectileSpreadAngle;
        public float projectileLateralOffset;
        public bool useParallelProjectiles;
        
        // Order-independent support gem effects cache
        public SupportGemProcessor.AggregatedEffects supportGemEffects;
        public bool supportGemEffectsValid;
    }

    // Multi-slot execution cache - dynamically supports any number of slots
    private readonly Dictionary<int, SkillExecutionCache> _executionCaches = new Dictionary<int, SkillExecutionCache>();
    
    // LRU tracking for bounded cache management
    private readonly LinkedList<int> _executionCacheLRU = new LinkedList<int>();
    private readonly Dictionary<int, LinkedListNode<int>> _executionCacheNodes = new Dictionary<int, LinkedListNode<int>>();
    
    private readonly LinkedList<int> _supportGemsCacheLRU = new LinkedList<int>();
    private readonly Dictionary<int, LinkedListNode<int>> _supportGemsCacheNodes = new Dictionary<int, LinkedListNode<int>>();
    
    private readonly LinkedList<int> _autonomousCacheLRU = new LinkedList<int>();
    private readonly Dictionary<int, LinkedListNode<int>> _autonomousCacheNodes = new Dictionary<int, LinkedListNode<int>>();

    // PlayerStats calculation caching
    private float _cachedPlayerDamageIncreased;
    private float _cachedPlayerDamageMultiplier;
    private int _playerStatsCacheFrame = -1;

    private Camera mainCamera;
    
    private void Awake()
    {
        if (equipmentPanel == null)
            equipmentPanel = FindFirstObjectByType<EquipmentPanel>();
            
        if (playerStats == null)
            playerStats = PlayerManager.PlayerStats;
            
        mainCamera = Camera.main;

        // Initialize random vector pool for zero-GC random spread calculations
        InitializeRandomVectorPool();

        // Initialize shared enemy detection system
        _sharedEnemyDetection = new SharedEnemyDetection
        {
            playerPosition = Vector2.zero,
            allEnemiesInMaxRange = new List<ICollidable>(50),
            maxRange = 0f,
            lastUpdateFrame = -1
        };

        // Initialize strategy factory
        _strategyFactory = new SkillExecutorFactory();
        _strategyFactory.SetDebugLogging(enableDebugLogging);
        _strategyFactory.Initialize();

        if (enableDebugLogging)
        {
            Debug.Log("[SkillExecutor] Strategy pattern initialized");
        }
        
        // Subscribe to cache invalidation events
        SkillCacheEvents.OnSkillGemChanged += InvalidateCache;
        SkillCacheEvents.OnSupportGemsChanged += OnSupportGemsChanged;
        SkillCacheEvents.OnAllEquipmentChanged += InvalidateAllCaches;
        SkillCacheEvents.OnPlayerStatsChanged += InvalidatePlayerStatsCache;
        SkillCacheEvents.OnPlayerBuffsChanged += InvalidatePlayerStatsCache;
    }
    
    private void OnDestroy()
    {
        // Exception-safe cleanup of pooled effects before destruction
        foreach (var kvp in _executionCaches)
        {
            var cache = kvp.Value;
            if (cache.supportGemEffectsValid)
            {
                try
                {
                    cache.supportGemEffects.ReturnToPool();
                }
                catch (System.InvalidOperationException ex)
                {
                    Debug.LogWarning($"[SkillExecutor] Pool return failed for slot {kvp.Key} during destruction: {ex.Message}");
                }
            }
        }
        _executionCaches.Clear();

        // Unsubscribe from cache invalidation events
        SkillCacheEvents.OnSkillGemChanged -= InvalidateCache;
        SkillCacheEvents.OnSupportGemsChanged -= OnSupportGemsChanged;
        SkillCacheEvents.OnSupportGemsChanged -= UpdateRuthlessHitCounter;
        SkillCacheEvents.OnAllEquipmentChanged -= InvalidateAllCaches;
        SkillCacheEvents.OnPlayerStatsChanged -= InvalidatePlayerStatsCache;
        SkillCacheEvents.OnPlayerBuffsChanged -= InvalidatePlayerStatsCache;
    }

    private void Start(){
        if(playerStats == null){
            playerStats = PlayerManager.PlayerStats;
        }

        // Enable fork debug logging if debug is enabled
        if (enableDebugLogging)
        {
            StatusEffectHelper.SetForkDebugLogging(true);
            Debug.Log("Fork projectile debug logging enabled via SkillExecutor");
        }

        // Subscribe to support gem changes for Ruthless hit counter management
        SkillCacheEvents.OnSupportGemsChanged += UpdateRuthlessHitCounter;
        SkillCacheEvents.OnSkillGemChanged += UpdateRuthlessHitCounter;
        SkillCacheEvents.OnAllEquipmentChanged += UpdateAllRuthlessHitCounters;
    }
    
    private void Update()
    {
        // Cache Time.time once per frame to avoid multiple calls
        _cachedFrameTime = Time.time;

        // Periodically scan equipment for autonomous skills (once per second)
        if (_cachedFrameTime - _lastAutonomousScanTime >= 1f)
        {
            RefreshAutonomousSkills();
            _lastAutonomousScanTime = _cachedFrameTime;
        }

        // Update cooldowns using zero-GC approach - collect changes first, then apply
        _reusableKeysList.Clear();
        _cooldownUpdates.Clear();
        
        foreach (var kvp in skillCooldowns)
        {
            float updatedCooldown = kvp.Value - Time.deltaTime;
            if (updatedCooldown <= 0)
            {
                _reusableKeysList.Add(kvp.Key); // Collect keys to remove
            }
            else
            {
                _cooldownUpdates[kvp.Key] = updatedCooldown; // Collect updates
            }
        }
        
        // Apply updates after enumeration to avoid collection modification during iteration
        foreach (var kvp in _cooldownUpdates)
        {
            skillCooldowns[kvp.Key] = kvp.Value;
        }
        
        // Remove expired cooldowns
        foreach (var key in _reusableKeysList)
        {
            skillCooldowns.Remove(key);
        }

        // Update autonomous skills
        UpdateAutonomousSkills();
    }
    
    /// <summary>
    /// Attempts to execute a skill using the strategy pattern.
    /// Handles mana cost, cooldowns, autonomous blocking, and strategy delegation.
    /// Preserves all existing functionality including spell echo and support gem effects.
    /// </summary>
    /// <param name="slotIndex">Equipment slot index containing the skill gem</param>
    /// <returns>True if skill was executed successfully, false otherwise</returns>
    public bool TryExecuteSkill(int slotIndex)
    {
        if (equipmentPanel == null) return false;
        
        // Check cooldown
        if (skillCooldowns.ContainsKey(slotIndex) && skillCooldowns[slotIndex] > 0)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"Skill in slot {slotIndex} is on cooldown: {skillCooldowns[slotIndex]:F1}s remaining");
            }
            return false;
        }
        
        // Get or cache the controller with cache validation
        var controller = GetCachedController(slotIndex);
        if (controller == null || controller.skillGemInstance == null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"No skill gem equipped in slot {slotIndex}");
            }
            return false;
        }
        
        var skillData = controller.skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null || skillData.skillPrefab == null)
        {
            Debug.LogError($"Skill gem in slot {slotIndex} has no prefab assigned!");
            return false;
        }

        // Check for autonomous support gems FIRST (before blocking manual execution)
        CheckAndStartAutonomous(slotIndex, controller, skillData);

        // Check if skill has autonomous support - block manual execution
        if (HasAutonomousSupport(controller, slotIndex))
        {
            if (enableDebugLogging)
            {
                string skillName = skillData?.gemName ?? "Unknown Skill";
                float range = GetAutonomousRange(controller, slotIndex);
                Debug.Log($"[SkillExecutor] Manual execution blocked for slot {slotIndex} ({skillName}): " +
                         $"Skill has autonomous support gem equipped (Range: {range:F1}). " +
                         $"Skill will auto-cast when enemies are within range.");
            }
            return false;
        }

        // Check mana cost
        float manaCost = controller.CalculateFinalManaCost();
        if (playerStats.currentMana < manaCost)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"Not enough mana. Required: {manaCost}, Current: {playerStats.currentMana}");
            }
            return false;
        }

        // Get cached mouse position for targeting
        Vector3 mousePosition = GetCachedMouseWorldPosition();
        
        // Execute skill using strategy pattern
        var executor = _strategyFactory.GetExecutor(skillData.skillType);
        if (executor != null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Strategy execution: {executor.GetType().Name} for {skillData.skillType}");
            }
            executor.Execute(this, slotIndex, controller, skillData, mousePosition, false);
        }
        else
        {
            Debug.LogError($"[SkillExecutor] No strategy found for {skillData.skillType}!");
        }
        
        // Consume mana
        playerStats.SpendMana(manaCost);
        
        // Set cooldown (apply attack speed multiplier from gems and player stats)
        float cooldown = controller.CalculateFinalCooldown();
        float gemAttackSpeed = controller.CalculateFinalAttackSpeed();
        float playerAttackSpeed = playerStats?.GetCalculatedStat(StatType.AttackSpeed) ?? 1f;
        
        // Combine gem and player attack speed multipliers
        float totalAttackSpeed = gemAttackSpeed * playerAttackSpeed;
        
        if (totalAttackSpeed > 0)
        {
            cooldown /= totalAttackSpeed;
        }
        if (cooldown > 0)
        {
            skillCooldowns[slotIndex] = cooldown;
            
            // Debug logging for manual skill cooldown
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] MANUAL cooldown set for slot {slotIndex}: " +
                         $"Base: {controller.CalculateFinalCooldown():F2}s, " +
                         $"After attack speed ({totalAttackSpeed:F2}x): {cooldown:F2}s");
            }
        }
        
        // Check for spell echo support gems using cached support gems
        CheckAndStartSpellEcho(slotIndex, controller, skillData);

        return true;
    }


    
    internal void ExecuteInstantSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition, int slotIndex)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        CacheSkillExecutionValues(controller, slotIndex);

        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);

        // For autonomous casts we always want to use the provided target position even if
        // the skill normally spawns at the player. This fixes Lightning Strike targeting.
        bool useTargetPosition = skillData.targetGroundPosition || HasAutonomousSupport(controller, slotIndex);
        Vector3 baseSpawnPosition = useTargetPosition ? targetPosition : transform.position;
        
        // Instant skills always spawn single instance (Spell Echo handles repetition over time)
        // Multiple projectiles support only affects projectile skills, not instant skills
        int spellCount = 1;
        
        // Spawn single instant spell at exact target position
        // (Spell Echo will handle additional casts over time)
        Vector3 spawnPosition = baseSpawnPosition;
        
        GameObject skillObject = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, Quaternion.identity);
        if (skillObject == null) return;
            
            // Check if this is an InstantSpell component
            if (PoolManager.Instance.GetCachedComponent<InstantSpell>(skillObject, out var instantSpell))
            {
                CollisionLayers layer = skillData.projectileLayer; // Use gem's configured layer

                // Use cached crit stats
                instantSpell.critChance = executionCache.finalCritChance;
                instantSpell.critMultiplier = executionCache.finalCritMultiplier;
                instantSpell.damageType = executionCache.damageBreakdown.GetPredominantType(); // Use predominant type after conversion
                instantSpell.ailmentChance = skillData.ailmentChance;
                instantSpell.damageBreakdown = executionCache.damageBreakdown; // Pass full breakdown

                // Pass gem data for status effect configuration
                instantSpell.skillGemData = skillData;
                instantSpell.supportGems = GetCachedSupportGems(controller, slotIndex);

                // Pass actual damage value, not multiplier
                instantSpell.Initialize((Vector2)spawnPosition, executionCache.playerModifiedDamage, layer);
            }
            else
            {
                // Fallback for other instant skill types
                ApplySkillEffects(skillObject, controller, slotIndex);
                
                // Handle area damage if supported using cached values
                if (executionCache.hasAreaDamage)
                {
                    ApplyAreaDamage(spawnPosition, executionCache.playerModifiedDamage, executionCache.areaRadius, slotIndex);
                }
            }
    }
    
    internal void ExecuteProjectileSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition, int slotIndex)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        CacheSkillExecutionValues(controller, slotIndex);

        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);

        _cachedDirectionVector = (targetPosition - transform.position).normalized;
        float baseAngle = Mathf.Atan2(_cachedDirectionVector.y, _cachedDirectionVector.x) * Mathf.Rad2Deg;

        // Use cached projectile values from order-independent processing
        int projectileCount = executionCache.totalProjectileCount;
        bool useParallel = executionCache.useParallelProjectiles;
        
        // Spawn multiple projectiles
        for (int i = 0; i < projectileCount; i++)
        {
            Vector3 spawnPosition;
            Quaternion rotation;
            Vector2 direction;

            if (useParallel)
            {
                // Parallel projectiles - use cached lateral offset
                float lateralOffset = executionCache.projectileLateralOffset;
                float totalWidth = (projectileCount - 1) * lateralOffset;
                float offsetAmount = -totalWidth / 2f + (i * lateralOffset);

                // Calculate perpendicular vector for offset using cached vector
                _cachedPerpendicularVector.Set(-_cachedDirectionVector.y, _cachedDirectionVector.x);
                _cachedPerpendicularVector *= offsetAmount;
                spawnPosition = transform.position + (Vector3)_cachedPerpendicularVector;

                // All parallel projectiles use the same angle and direction
                rotation = Quaternion.Euler(0, 0, baseAngle);
                direction = _cachedDirectionVector;
            }
            else
            {
                // Angular spread - use cached value
                float spreadAngle = executionCache.projectileSpreadAngle;
                float angleStep = projectileCount > 1 ? spreadAngle : 0;
                float startAngle = baseAngle - (angleStep * (projectileCount - 1) / 2f);
                float currentAngle = startAngle + (angleStep * i);

                rotation = Quaternion.Euler(0, 0, currentAngle);
                float radians = currentAngle * Mathf.Deg2Rad;
                // Use cached vector for direction calculation
                _cachedVector2Temp.Set(Mathf.Cos(radians), Mathf.Sin(radians));
                direction = _cachedVector2Temp;
                spawnPosition = transform.position;
            }
            
            GameObject projectileObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, rotation);
            if (projectileObj == null) continue;
            
            // Configure projectile using cached values
            if (PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var projectile))
            {
                // Validate cached damage to prevent 0 damage issue
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                 
                    // Recalculate if cache is invalid
                    CacheSkillExecutionValues(controller, slotIndex);
                    executionCache = GetExecutionCache(slotIndex);
                    finalDamage = executionCache.playerModifiedDamage;
                    
                    if (finalDamage <= 0f)
                    {
                        // Fallback to base damage if still invalid
                        finalDamage = skillData.baseDamage;
                      
                    }
                }
                
            
                
                // Initialize projectile FIRST (this might reset values)
                CollisionLayers layer = skillData.projectileLayer; // Use gem's configured layer
                int layerIndex = CollisionLayersHelper.ToUnityLayerIndex(layer); // Convert to Unity layer index
                projectile.Initialize((Vector2)spawnPosition, direction, finalDamage, layerIndex, skillData.projectileSpeed, skillData.duration);
                
                // THEN set all values AFTER initialization to prevent overwrites
                projectile.damage = finalDamage;
                projectile.speed = skillData.projectileSpeed;
                projectile.lifetime = skillData.duration;
                projectile.critChance = executionCache.finalCritChance;
                projectile.critMultiplier = executionCache.finalCritMultiplier;
                projectile.damageType = executionCache.damageBreakdown.GetPredominantType(); // Use predominant type after conversion
                projectile.ailmentChance = skillData.ailmentChance;
                projectile.damageBreakdown = executionCache.damageBreakdown; // Pass full damage breakdown
                
                // Pass gem data for status effect configuration
                projectile.skillGemData = skillData;
                projectile.supportGems = GetCachedSupportGems(controller, slotIndex);

                // Apply support gem effects using cached values
                if (executionCache.hasPierce)
                {
                    projectile.SetPiercing(true);
                }

                if (executionCache.hasChain)
                {
                    projectile.SetChaining(true, executionCache.chainCount);
                }

                if (executionCache.hasFork)
                {
                    projectile.SetFork(true, executionCache.forkCount, executionCache.forkAngle);
                }

                if (executionCache.hasAreaDamage)
                {
                    projectile.SetAreaDamage(true, executionCache.areaRadius);
                }
                
                // Double-check damage after initialization
                if (projectile.damage <= 0f)
                {
                    Debug.LogError($"[SkillExecutor] Projectile damage is 0 after initialization! Setting to fallback: {skillData.baseDamage}");
                    projectile.damage = skillData.baseDamage;
                }
            }
        }
    }
    
    internal void ApplySkillEffects(GameObject skillObject, GemSocketController controller, int slotIndex)
    {
        // Apply damage multipliers and effects to the skill object
        // This would be customized based on your skill system

        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);

        // Use cached damage calculation (assumes CacheSkillExecutionValues was called)
        float totalDamage = executionCache.isValid ? executionCache.playerModifiedDamage :
                           GetCachedPlayerDamageModifiers(controller.skillGemInstance?.GetSkillDamage() ?? 0f, controller);

        // Use zero-allocation approach to set damage on specific known components
        // This avoids GetComponentsInChildren allocation and leverages PoolManager caching
        
        // Try common damage dealer component types first (most skills use these)
        if (PoolManager.Instance.GetCachedComponent<InstantSpell>(skillObject, out var instantSpell))
        {
            // InstantSpell has a public damage property - set it directly
            instantSpell.damage = totalDamage;
        }
        else if (PoolManager.Instance.GetCachedComponent<Projectile>(skillObject, out var projectile))
        {
            projectile.damage = totalDamage;
        }
        // Add other specific component types as needed based on actual skill implementations
        // This approach is more performant than interface-based GetComponentsInChildren
    }
    
    internal void ApplyAreaDamage(Vector3 center, float damage, float radius, int slotIndex)
    {
        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);
        
        // Use Physics2D to find enemies in radius
        Collider2D[] colliders = Physics2D.OverlapCircleAll(center, radius);
        
        // Clear cache and populate with enemy colliders
        _nearbyEnemiesCache.Clear();
        foreach (var collider in colliders)
        {
            if (collider.CompareTag("Enemy"))
            {
                // Create a wrapper for the enemy (assuming it has the required components)
                // Note: This is a simplified approach - may need adjustment based on actual ICollidable implementation
                if (collider.TryGetComponent<BaseEnemy>(out var enemy))
                {
                    // Add to cache if enemy has proper interface implementation
                    // This part may need adjustment based on your ICollidable implementation
                }
            }
        }
            
        // Create damage info once for all targets
        DamageInfo damageInfo;
        if (executionCache.damageBreakdown.TotalDamage > 0)
        {
            damageInfo = DamageInfo.FromBreakdown(
                executionCache.damageBreakdown,
                false,
                1f,
                "SkillExecutor_Area"
            );
        }
        else
        {
            // Fallback to original damage type instead of hardcoded Physical
            var originalType = executionCache.originalDamageType;
            if (originalType == default)
            {
                Debug.LogWarning("[SkillExecutor] originalDamageType not set in cache, defaulting to Physical damage");
                originalType = DamageType.Physical;
            }
            damageInfo = DamageInfo.FromSingleType(damage, originalType, false, 1f, "SkillExecutor_Area");
        }
            
        foreach (var collidable in _nearbyEnemiesCache)
        {
            var target = collidable.GameObject;
            
            // Apply damage using the same system as projectiles
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(damageInfo);
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
            {
                combatantHealth.TakeDamage(damageInfo);
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(damageInfo);
            }
        }
    }
    
    public void InvalidateCache(int slotIndex)
    {
        // Exception-safe cache invalidation with guaranteed cleanup
        try 
        {
            if (_executionCaches.TryGetValue(slotIndex, out var cache) && cache.supportGemEffectsValid)
            {
                cache.supportGemEffects.ReturnToPool();
            }
        }
        catch (System.InvalidOperationException ex)
        {
            Debug.LogWarning($"[SkillExecutor] Pool return failed for slot {slotIndex}: {ex.Message}");
        }
        finally 
        {
            // Always cleanup regardless of pool return success
            _executionCaches.Remove(slotIndex);
            cachedControllers.Remove(slotIndex);
            _cachedSupportGems.Remove(slotIndex);

            // Invalidate autonomous support caches
            _cachedAutonomousStatus.Remove(slotIndex);
            _cachedAutonomousRange.Remove(slotIndex);
            _cachedAutonomousInterval.Remove(slotIndex);

            // Also remove any autonomous skill data for this slot
            RemoveAutonomousSkillBySlot(slotIndex);
        }
    }

    public void InvalidateAllCaches()
    {
        // Exception-safe cleanup of all pooled effects
        foreach (var kvp in _executionCaches)
        {
            var cache = kvp.Value;
            if (cache.supportGemEffectsValid)
            {
                try
                {
                    cache.supportGemEffects.ReturnToPool();
                }
                catch (System.InvalidOperationException ex)
                {
                    Debug.LogWarning($"[SkillExecutor] Pool return failed for slot {kvp.Key} during full invalidation: {ex.Message}");
                }
            }
        }

        // Always clear all caches regardless of cleanup success
        _executionCaches.Clear();
        cachedControllers.Clear();
        _cachedSupportGems.Clear();

        // Clear autonomous support caches
        _cachedAutonomousStatus.Clear();
        _cachedAutonomousRange.Clear();
        _cachedAutonomousInterval.Clear();

        _mousePositionCacheFrame = -1;
        _playerStatsCacheFrame = -1;
    }
    
    private void InvalidatePlayerStatsCache()
    {
        _playerStatsCacheFrame = -1;
        // Invalidate all execution caches as they depend on player stats
        // Use array to avoid ToList() allocation
        var slotIndices = new int[_executionCaches.Count];
        int index = 0;
        foreach (var kvp in _executionCaches)
        {
            slotIndices[index++] = kvp.Key;
        }
        
        for (int i = 0; i < index; i++)
        {
            int slotIndex = slotIndices[i];
            if (_executionCaches.TryGetValue(slotIndex, out var cache))
            {
                cache.isValid = false;
                cache.originalDamageType = default;
                _executionCaches[slotIndex] = cache;
            }
        }
    }
    
    private void OnSupportGemsChanged(int slotIndex)
    {
        // Invalidate cache for the slot
        InvalidateCache(slotIndex);
        
        // Force UI to refresh this slot immediately
        if (equipmentPanel != null)
        {
            equipmentPanel.RefreshSlot(slotIndex);
        }
        
        // Force immediate refresh of autonomous skills for this slot
        RefreshAutonomousSkillForSlot(slotIndex);
    }
    
    private void RefreshAutonomousSkillForSlot(int slotIndex)
    {
        if (equipmentPanel == null) return;
        
        // Remove existing autonomous skill for this slot
        RemoveAutonomousSkillBySlot(slotIndex);
        
        // Check if the slot still has autonomous support
        var controller = GetCachedController(slotIndex);
        if (controller == null || !HasAutonomousSupport(controller, slotIndex))
            return;
            
        var skillData = controller.skillGemInstance?.gemDataTemplate as SkillGemData;
        if (skillData == null)
            return;
            
        // Re-add the autonomous skill with fresh data
        CheckAndStartAutonomous(slotIndex, controller, skillData);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] Refreshed autonomous skill for slot {slotIndex} after support gem change");
        }
    }

    /// <summary>
    /// Gets cached mouse world position, avoiding repeated ScreenToWorldPoint calls within the same frame
    /// </summary>
    private Vector3 GetCachedMouseWorldPosition()
    {
        int currentFrame = Time.frameCount;
        if (_mousePositionCacheFrame != currentFrame)
        {
            _cachedMouseScreenPos = Mouse.current.position.ReadValue();
            _cachedVector3Temp.Set(_cachedMouseScreenPos.x, _cachedMouseScreenPos.y, mainCamera.nearClipPlane);
            _lastCachedMousePosition = mainCamera.ScreenToWorldPoint(_cachedVector3Temp);
            _lastCachedMousePosition.z = 0;
            _mousePositionCacheFrame = currentFrame;
        }
        return _lastCachedMousePosition;
    }

    /// <summary>
    /// Gets cached support gems for a slot, reducing repeated method calls with event-driven invalidation
    /// Implements LRU cache management to prevent unbounded growth
    /// </summary>
    internal List<GemInstance> GetCachedSupportGems(GemSocketController controller, int slotIndex)
    {
        // Simple cache check - no time validation
        if (_cachedSupportGems.TryGetValue(slotIndex, out var supportGems) && supportGems != null)
        {
            // Update LRU order
            UpdateSupportGemsCacheLRU(slotIndex);
            return supportGems;
        }

        // Check if we need to evict old entries before adding new one
        EvictSupportGemsCacheIfNeeded();

        // Cache miss - recalculate
        supportGems = controller.GetCompatibleSupportGems();
        
        // Respect tag compatibility - no fallback to ALL support gems
        // The tag system should be the authoritative source for compatibility
        
        _cachedSupportGems[slotIndex] = supportGems;
        
        // Add to LRU tracking
        var node = _supportGemsCacheLRU.AddFirst(slotIndex);
        _supportGemsCacheNodes[slotIndex] = node;
        
        return supportGems;
    }

    /// <summary>
    /// Gets cached controller for a slot with event-driven invalidation
    /// </summary>
    private GemSocketController GetCachedController(int slotIndex)
    {
        // Simple cache check - no time validation
        if (cachedControllers.TryGetValue(slotIndex, out var controller) && controller != null)
        {
            return controller;
        }

        // Cache miss - recalculate
        controller = equipmentPanel?.GetActiveSkillController(slotIndex);
        if (controller != null)
        {
            cachedControllers[slotIndex] = controller;
        }
        else
        {
            // Remove invalid entries from cache
            cachedControllers.Remove(slotIndex);
        }

        return controller;
    }

    /// <summary>
    /// Gets execution cache for a specific slot, creating if needed (multi-slot support)
    /// Implements LRU cache management to prevent unbounded growth
    /// </summary>
    internal SkillExecutionCache GetExecutionCache(int slotIndex)
    {
        if (!_executionCaches.TryGetValue(slotIndex, out var cache))
        {
            // Check if we need to evict old entries
            EvictExecutionCacheIfNeeded();
            
            cache = new SkillExecutionCache { slotIndex = slotIndex };
            _executionCaches[slotIndex] = cache;
            
            // Add to LRU tracking
            var node = _executionCacheLRU.AddFirst(slotIndex);
            _executionCacheNodes[slotIndex] = node;
        }
        else
        {
            // Move to front of LRU list (most recently used)
            UpdateExecutionCacheLRU(slotIndex);
        }
        return cache;
    }

    /// <summary>
    /// Sets execution cache for a specific slot (multi-slot support)
    /// </summary>
    private void SetExecutionCache(int slotIndex, SkillExecutionCache cache)
    {
        _executionCaches[slotIndex] = cache;
    }

    /// <summary>
    /// Pre-calculates all expensive GemSocketController values for the current skill execution
    /// Uses order-independent support gem processing to eliminate socket order dependencies
    /// Implements correct damage formula: (base + flat) × (increased + increased) × (more × more) × playerMultiplier
    /// Multi-slot support - each slot maintains its own execution cache
    /// </summary>
    internal void CacheSkillExecutionValues(GemSocketController controller, int slotIndex)
    {
        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);
        
        if (executionCache.isValid && executionCache.slotIndex == slotIndex && executionCache.supportGemEffectsValid)
            return;

        // Return previous pooled effects to pool before replacing
        if (executionCache.supportGemEffectsValid)
        {
            executionCache.supportGemEffects.ReturnToPool();
        }

        // Process support gems using zero-GC method
        var supportGems = GetCachedSupportGems(controller, slotIndex);
        executionCache.supportGemEffects = SupportGemProcessor.ProcessSupportGemsZeroGC(supportGems);
        executionCache.supportGemEffectsValid = true;

        // Use order-independent results instead of controller methods
        var effects = executionCache.supportGemEffects;
        
        // Get base values directly from skill gem data (before support gem modifications)
        var skillData = controller.skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData != null)
        {
            // Apply correct damage formula: (base + flat Physical) × (1 + totalIncreased/100) × (supportMore × playerMore)
            float baseDamage = controller.skillGemInstance.GetSkillDamage();
            float flatPhysicalDamage = controller.GetTotalFlatDamage();
            
            // Debug input values for damage calculation
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Pre-calculation values for '{skillData.gemName}':");
                Debug.Log($"  skillGemInstance null: {controller.skillGemInstance == null}");
                Debug.Log($"  skillGemInstance.GetSkillDamage(): {baseDamage}");
                Debug.Log($"  controller.GetTotalFlatDamage(): {flatPhysicalDamage}");
                Debug.Log($"  skillData.baseDamage: {skillData.baseDamage}");
            }
            
            executionCache.originalDamageType = skillData.damageType;
            
            // Calculate damage breakdown using the improved system with separate flat Physical tracking
            executionCache.damageBreakdown = DamageCalculator.Calculate(
                baseDamage,
                skillData.damageType,
                flatPhysicalDamage,
                effects,
                playerStats
            );
            
            // Debug damage calculation for troubleshooting
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Damage calculation for '{skillData.gemName}':");
                Debug.Log($"  BaseDamage: {baseDamage}");
                Debug.Log($"  DamageType: {skillData.damageType}");
                Debug.Log($"  FlatPhysicalDamage: {flatPhysicalDamage}");
                Debug.Log($"  PlayerStats null: {playerStats == null}");
                // Note: effects is a struct so it cannot be null
                Debug.Log($"  Effects.hasConversion: {effects.hasConversion}");
                Debug.Log($"  Effects.totalDamageIncrease: {effects.totalDamageIncrease}");
                Debug.Log($"  Effects.damageMoreMultipliers count: {effects.damageMoreMultipliers?.Count ?? 0}");
                Debug.Log($"  Effects.hasPierce: {effects.hasPierce}");
                Debug.Log($"  Effects.hasChain: {effects.hasChain}");
                Debug.Log($"  Breakdown TotalDamage: {executionCache.damageBreakdown.TotalDamage}");
                Debug.Log($"  Breakdown Physical: {executionCache.damageBreakdown.physicalDamage}");
                Debug.Log($"  Breakdown Fire: {executionCache.damageBreakdown.fireDamage}");
                Debug.Log($"  Breakdown Ice: {executionCache.damageBreakdown.iceDamage}");
                Debug.Log($"  Breakdown Lightning: {executionCache.damageBreakdown.lightningDamage}");
            }

            // Log damage calculation steps if debugging is enabled
            if (enableDebugLogging)
            {
                DamageCalculator.LogCalculationSteps(
                    baseDamage + flatPhysicalDamage,
                    skillData.damageType,
                    effects,
                    executionCache.damageBreakdown
                );
            }

            // Apply Ruthless hit multiplier if applicable
            float ruthlessMultiplier = 1f;
            if (effects.hasRuthlessHits && RuthlessHitCounter.Instance != null)
            {
                bool isRuthlessHit = RuthlessHitCounter.Instance.RecordHitAndCheck(slotIndex);
                if (isRuthlessHit)
                {
                    ruthlessMultiplier = effects.ruthlessDamageMultiplier;
                    if (enableDebugLogging)
                    {
                        Debug.Log($"[SkillExecutor] Ruthless hit! Damage multiplier: {ruthlessMultiplier:F2}x");
                    }
                }
            }

            // Store total damage for compatibility
            executionCache.finalDamage = executionCache.damageBreakdown.TotalDamage * ruthlessMultiplier;
            executionCache.playerModifiedDamage = executionCache.finalDamage;

            // Log damage output for debugging
            if (enableDamageLogging)
            {
                string skillName = skillData?.gemName ?? "Unknown Skill";
                DamageLogger.LogDamageWithBreakdown(skillName, executionCache.finalDamage,
                    executionCache.damageBreakdown, $"Slot {slotIndex}",
                    ruthlessMultiplier > 1f, "RUTHLESS");
            }
            
            // Calculate final crit stats using order-independent multipliers
            executionCache.finalCritChance = skillData.critChance + effects.totalCritChanceIncrease;
            executionCache.finalCritMultiplier = SupportGemProcessor.CalculateMultipliedValue(skillData.critMultiplier, effects.critMultipliers);
        }
        else
        {
            // Fallback to original calculation methods if skill data is unavailable
            executionCache.finalDamage = controller.CalculateFinalDamage();
            executionCache.finalCritChance = controller.CalculateFinalCritChance();
            executionCache.finalCritMultiplier = controller.CalculateFinalCritMultiplier();
            
            // Set originalDamageType from the gem template if available
            var gemData = controller.skillGemInstance?.gemDataTemplate as SkillGemData;
            executionCache.originalDamageType = gemData?.damageType ?? DamageType.Physical;
            
            // Apply player modifiers to base damage (not already-modified damage)
            float baseDamage = controller.skillGemInstance?.GetSkillDamage() ?? executionCache.finalDamage;
            executionCache.playerModifiedDamage = GetCachedPlayerDamageModifiers(baseDamage, controller);

            // Log damage output for debugging (fallback path)
            if (enableDamageLogging)
            {
                var fallbackGemData = controller.skillGemInstance?.gemDataTemplate as SkillGemData;
                string skillName = fallbackGemData?.gemName ?? "Unknown Skill";
                DamageLogger.LogDamage(skillName, executionCache.playerModifiedDamage,
                    executionCache.originalDamageType, $"Slot {slotIndex} (Fallback)");
            }
        }
        
        // Use aggregated boolean effects and combine with intrinsic skill properties
        executionCache.hasPierce = effects.hasPierce;
        executionCache.hasChain = effects.hasChain;
        executionCache.hasFork = effects.hasFork;
        executionCache.hasAreaDamage = effects.hasAreaDamage;
        
        // Add intrinsic skill mechanics if present
        if (controller.skillGemInstance?.gemDataTemplate is SkillGemData intrinsicSkillData)
        {
            executionCache.hasPierce = executionCache.hasPierce || intrinsicSkillData.intrinsicHasPierce;
            executionCache.hasChain = executionCache.hasChain || intrinsicSkillData.intrinsicHasChain;
        }
        
        // Use max values from aggregated effects and combine with intrinsic values
        executionCache.chainCount = effects.maxChainCount;
        executionCache.forkCount = effects.maxForkCount;
        executionCache.forkAngle = effects.maxForkAngle;
        executionCache.areaRadius = effects.maxAreaRadius;
        
        // Override with intrinsic chain count if skill has intrinsic chaining
        if (controller.skillGemInstance?.gemDataTemplate is SkillGemData intrinsicSkillData2 && 
            intrinsicSkillData2.intrinsicHasChain && intrinsicSkillData2.intrinsicChainCount > executionCache.chainCount)
        {
            executionCache.chainCount = intrinsicSkillData2.intrinsicChainCount;
        }
        
        // Cache projectile effects from aggregated results
        executionCache.totalProjectileCount = 1 + effects.totalExtraProjectiles; // Base 1 + extras
        executionCache.projectileSpreadAngle = effects.maxProjectileSpreadAngle;
        executionCache.projectileLateralOffset = effects.maxProjectileLateralOffset > 0f ? effects.maxProjectileLateralOffset : 0.6f; // Default 0.6f if not set
        executionCache.useParallelProjectiles = effects.useParallelProjectiles;

        executionCache.isValid = true;
        executionCache.slotIndex = slotIndex;
        
        // Store updated cache back to dictionary
        SetExecutionCache(slotIndex, executionCache);
    }

    /// <summary>
    /// Gets cached player damage modifiers, avoiding repeated PlayerStats calculations
    /// Used only for fallback calculations when SupportGemProcessor method fails
    /// </summary>
    private float GetCachedPlayerDamageModifiers(float baseDamage, GemSocketController controller)
    {
        if (playerStats == null) return baseDamage;

        // Cache PlayerStats calculations per frame
        int currentFrame = Time.frameCount;
        if (_playerStatsCacheFrame != currentFrame)
        {
            _cachedPlayerDamageIncreased = playerStats.GetCalculatedStat(StatType.DamageIncreased);
            _cachedPlayerDamageMultiplier = playerStats.GetCalculatedStat(StatType.DamageMultiplier);
            _playerStatsCacheFrame = currentFrame;
        }

        // WARNING: This method should only be used for fallback calculations
        // Main damage calculation should use the corrected formula in CacheSkillExecutionValues
        
        // Apply only player modifiers (support gems should already be applied)
        float damageWithPlayerIncreased = baseDamage * (1f + _cachedPlayerDamageIncreased / 100f);
        float finalDamage = damageWithPlayerIncreased * _cachedPlayerDamageMultiplier;

        return finalDamage;
    }
    
    /// <summary>
    /// Gets cached player damage increased stat
    /// </summary>
    private float GetCachedPlayerDamageIncreased()
    {
        if (playerStats == null) return 0f;

        // Cache PlayerStats calculations per frame
        int currentFrame = Time.frameCount;
        if (_playerStatsCacheFrame != currentFrame)
        {
            _cachedPlayerDamageIncreased = playerStats.GetCalculatedStat(StatType.DamageIncreased);
            _cachedPlayerDamageMultiplier = playerStats.GetCalculatedStat(StatType.DamageMultiplier);
            _playerStatsCacheFrame = currentFrame;
        }

        return _cachedPlayerDamageIncreased;
    }
    
    /// <summary>
    /// Gets cached player damage multiplier stat
    /// </summary>
    private float GetCachedPlayerDamageMultiplier()
    {
        if (playerStats == null) return 1f;

        // Cache PlayerStats calculations per frame
        int currentFrame = Time.frameCount;
        if (_playerStatsCacheFrame != currentFrame)
        {
            _cachedPlayerDamageIncreased = playerStats.GetCalculatedStat(StatType.DamageIncreased);
            _cachedPlayerDamageMultiplier = playerStats.GetCalculatedStat(StatType.DamageMultiplier);
            _playerStatsCacheFrame = currentFrame;
        }

        return _cachedPlayerDamageMultiplier;
    }
    
    public bool IsSkillOnCooldown(int slotIndex)
    {
        return skillCooldowns.ContainsKey(slotIndex) && skillCooldowns[slotIndex] > 0;
    }
    
    public float GetRemainingCooldown(int slotIndex)
    {
        return skillCooldowns.TryGetValue(slotIndex, out float cooldown) ? cooldown : 0f;
    }
    
    private void CheckAndStartSpellEcho(int slotIndex, GemSocketController controller, SkillGemData skillData)
    {
        // Only check for spell echo if the skill has the Spell tag
        if ((skillData.gemTags & GemTag.Spell) == 0)
            return;
            
        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);
        
        // Use order-independent support gem effects from cache
        if (executionCache.supportGemEffectsValid && executionCache.supportGemEffects.hasSpellEcho)
        {
            var effects = executionCache.supportGemEffects;
            
            // Create spell echo data using order-independent results
            var echoData = new SpellEchoData
            {
                slotIndex = slotIndex,
                remainingEchoes = effects.spellEchoCount,
                // No longer storing targetPosition - uses current mouse position for each echo
                controller = controller,
                skillData = skillData,
                echoDelay = effects.spellEchoDelay,
                echoSpreadRadius = effects.spellEchoSpreadRadius
            };
            
            // Start the echo coroutine
            var echoCoroutine = StartCoroutine(ProcessSpellEcho(echoData));
            activeSpellEchoes.Add(echoCoroutine);
        }
    }
    
    private IEnumerator ProcessSpellEcho(SpellEchoData echoData)
    {
        while (echoData.remainingEchoes > 0)
        {
            // Wait for echo delay
            yield return new WaitForSeconds(echoData.echoDelay);

            // Check if the skill slot is still valid
            if (equipmentPanel == null || echoData.controller == null || echoData.skillData == null)
                break;

            // Get CURRENT mouse position for echo targeting using cached method
            Vector3 currentMousePosition = GetCachedMouseWorldPosition();

            if (enableDebugLogging)
            {
                Debug.Log($"Spell Echo: Targeting current mouse position {currentMousePosition}");
            }

            // Calculate echo position with optional spread from CURRENT mouse position
            Vector3 echoPosition = currentMousePosition;
            if (echoData.echoSpreadRadius > 0)
            {
                _cachedVector2Temp = GetCachedRandomVector() * echoData.echoSpreadRadius;
                _cachedVector3Temp.Set(_cachedVector2Temp.x, _cachedVector2Temp.y, 0);
                echoPosition += _cachedVector3Temp;
            }

            // Execute the echo cast (free - no mana cost)
            ExecuteEchoSkill(echoData.controller, echoData.skillData, echoPosition, echoData.slotIndex);

            echoData.remainingEchoes--;
        }

        // Remove null echoes from active list using cached indices to avoid lambda allocation
        CleanupNullEchoes();
    }

    /// <summary>
    /// Process autonomous spell echo with fixed target position
    /// </summary>
    private IEnumerator ProcessAutonomousSpellEcho(AutonomousSpellEchoData echoData)
    {
        while (echoData.remainingEchoes > 0)
        {
            // Wait for echo delay
            yield return new WaitForSeconds(echoData.echoDelay);

            // Check if the skill slot is still valid
            if (equipmentPanel == null || echoData.controller == null || echoData.skillData == null)
                break;

            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous Spell Echo: Targeting fixed position {echoData.targetPosition}");
            }

            // Calculate echo position with optional spread from FIXED target position
            Vector3 echoPosition = echoData.targetPosition;
            if (echoData.echoSpreadRadius > 0)
            {
                _cachedVector2Temp = GetCachedRandomVector() * echoData.echoSpreadRadius;
                _cachedVector3Temp.Set(_cachedVector2Temp.x, _cachedVector2Temp.y, 0);
                echoPosition += _cachedVector3Temp;
            }

            // Execute the echo cast (free - no mana cost)
            ExecuteEchoSkill(echoData.controller, echoData.skillData, echoPosition, echoData.slotIndex);

            echoData.remainingEchoes--;
        }

        // Remove null echoes from active list using cached indices to avoid lambda allocation
        CleanupNullEchoes();
    }

    private void ExecuteEchoSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition, int slotIndex)
    {
        // Execute echo skill using strategy pattern (no mana/cooldown costs for echoes)
        var executor = _strategyFactory.GetExecutor(skillData.skillType);
        if (executor != null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Echo strategy execution: {executor.GetType().Name} for {skillData.skillType}");
            }
            // Pass isAutonomous=false for echo casts (echoes follow original cast behavior)
            executor.Execute(this, slotIndex, controller, skillData, targetPosition, false);
        }
        else
        {
            Debug.LogError($"[SkillExecutor] No strategy found for echo {skillData.skillType}!");
        }
    }

    private void CheckAndStartAutonomous(int slotIndex, GemSocketController controller, SkillGemData skillData)
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] CheckAndStartAutonomous called for slot {slotIndex}, HasAutonomous: {HasAutonomousSupport(controller, slotIndex)}");
        }

        if (!HasAutonomousSupport(controller, slotIndex)) return;

        // Remove existing autonomous data for this slot using manual iteration to avoid lambda allocation
        RemoveAutonomousSkillBySlot(slotIndex);

        // Use optimized autonomous skill system with object pooling
        var optimizedSkill = GetPooledOptimizedSkill();
        optimizedSkill.slotIndex = slotIndex;
        optimizedSkill.controller = controller;
        optimizedSkill.skillData = skillData;
        optimizedSkill.range = GetAutonomousRange(controller, slotIndex);
        optimizedSkill.updateInterval = GetAutonomousUpdateInterval(controller, slotIndex);
        optimizedSkill.state = AutonomousState.Scanning;
        optimizedSkill.nextStateTime = _cachedFrameTime;
        optimizedSkill.cachedTarget = null;
        optimizedSkill.lastTargetValidationTime = 0f;

        optimizedAutonomousSkills.Add(optimizedSkill);

        // Also create legacy version for backward compatibility (until full migration is complete)
        var legacyData = GetPooledAutonomousSkill();
        legacyData.slotIndex = slotIndex;
        legacyData.controller = controller;
        legacyData.skillData = skillData;
        legacyData.range = optimizedSkill.range;
        legacyData.updateInterval = optimizedSkill.updateInterval;
        legacyData.lastUpdateTime = 0f;
        legacyData.isActive = true;

        activeAutonomousSkills.Add(legacyData);

        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] Started autonomous casting for skill in slot {slotIndex} with range {optimizedSkill.range}, interval {optimizedSkill.updateInterval}");
            Debug.Log($"[SkillExecutor] Total autonomous skills active: {activeAutonomousSkills.Count} legacy + {optimizedAutonomousSkills.Count} optimized");
        }
    }

    /// <summary>
    /// Gets a pooled AutonomousSkillData instance to reduce GC allocations
    /// </summary>
    private AutonomousSkillData GetPooledAutonomousSkill()
    {
        if (_autonomousSkillPool.Count > 0)
        {
            var pooled = _autonomousSkillPool.Pop();
            // Reset to default values
            pooled.slotIndex = -1;
            pooled.controller = null;
            pooled.skillData = null;
            pooled.range = 0f;
            pooled.updateInterval = 0f;
            pooled.lastUpdateTime = 0f;
            pooled.isActive = false;
            return pooled;
        }
        else
        {
            return new AutonomousSkillData();
        }
    }

    /// <summary>
    /// Gets a pooled OptimizedAutonomousSkill instance to reduce GC allocations
    /// </summary>
    private OptimizedAutonomousSkill GetPooledOptimizedSkill()
    {
        if (_optimizedSkillPool.Count > 0)
        {
            var pooled = _optimizedSkillPool.Pop();
            // Reset to default values
            pooled.slotIndex = -1;
            pooled.state = AutonomousState.Scanning;
            pooled.nextStateTime = 0f;
            pooled.cachedTarget = null;
            pooled.range = 0f;
            pooled.updateInterval = 0f;
            pooled.lastTargetValidationTime = 0f;
            pooled.controller = null;
            pooled.skillData = null;
            return pooled;
        }
        else
        {
            return new OptimizedAutonomousSkill();
        }
    }

    /// <summary>
    /// Returns an AutonomousSkillData instance to the pool
    /// </summary>
    private void ReturnAutonomousSkillToPool(AutonomousSkillData skill)
    {
        // Clear references to avoid memory leaks
        skill.controller = null;
        skill.skillData = null;
        
        _autonomousSkillPool.Push(skill);
    }

    /// <summary>
    /// Returns an OptimizedAutonomousSkill instance to the pool
    /// </summary>
    private void ReturnOptimizedSkillToPool(OptimizedAutonomousSkill skill)
    {
        // Clear references to avoid memory leaks
        skill.controller = null;
        skill.skillData = null;
        skill.cachedTarget = null;
        
        _optimizedSkillPool.Push(skill);
    }

    private void UpdateAutonomousSkills()
    {
        if (PlayerManager.PlayerTransform == null) return;

        Vector2 playerPos = PlayerManager.PlayerPosition;

        // Debug logging for autonomous skill count (using cached frame time)
        if (enableDebugLogging && (activeAutonomousSkills.Count > 0 || optimizedAutonomousSkills.Count > 0) && _cachedFrameTime % 5f < Time.deltaTime)
        {
            Debug.Log($"[SkillExecutor] Processing {activeAutonomousSkills.Count} legacy + {optimizedAutonomousSkills.Count} optimized autonomous skills");
        }

        // Single shared enemy detection query for all autonomous skills
        UpdateSharedEnemyDetection();

        // Process legacy autonomous skills (backward compatibility)
        foreach (var autonomous in activeAutonomousSkills)
        {
            // Check update interval using cached frame time
            if (_cachedFrameTime - autonomous.lastUpdateTime < autonomous.updateInterval)
                continue;

            autonomous.lastUpdateTime = _cachedFrameTime;

            // Try to cast at nearest enemy using shared detection results
            TryAutonomousCastOptimized(autonomous, playerPos);
        }

        // Process optimized autonomous skills with state machine
        for (int i = 0; i < optimizedAutonomousSkills.Count; i++)
        {
            var skill = optimizedAutonomousSkills[i];
            UpdateAutonomousSkillState(ref skill);
            optimizedAutonomousSkills[i] = skill; // Update the struct in the list
        }
    }

    /// <summary>
    /// Updates shared enemy detection data once per frame for all autonomous skills.
    /// Eliminates O(N²) spatial queries by performing single query per frame.
    /// </summary>
    private void UpdateSharedEnemyDetection()
    {
        if (_sharedEnemyDetection.lastUpdateFrame == Time.frameCount) return;

        // Get maximum range needed by any autonomous skill
        float maxRange = GetMaxAutonomousRange();
        if (maxRange <= 0f) return; // No autonomous skills active

        Vector2 playerPos = PlayerManager.PlayerPosition;
        
        // Single spatial query for all autonomous skills using Physics2D
        Collider2D[] colliders = Physics2D.OverlapCircleAll(playerPos, maxRange);
        
        _sharedEnemyDetection.allEnemiesInMaxRange.Clear();
        foreach (var collider in colliders)
        {
            if (collider.CompareTag("Enemy"))
            {
                // Add enemy to detection cache
                // Note: This may need adjustment based on ICollidable implementation
            }
        }

        _sharedEnemyDetection.playerPosition = playerPos;
        _sharedEnemyDetection.maxRange = maxRange;
        _sharedEnemyDetection.lastUpdateFrame = Time.frameCount;

        if (enableDebugLogging && _sharedEnemyDetection.allEnemiesInMaxRange.Count > 0)
        {
            Debug.Log($"[SkillExecutor] Shared enemy detection: Found {_sharedEnemyDetection.allEnemiesInMaxRange.Count} enemies within {maxRange:F1} range");
        }
    }

    /// <summary>
    /// Gets the maximum autonomous range needed by any active autonomous skill.
    /// Used to optimize shared enemy detection queries.
    /// </summary>
    private float GetMaxAutonomousRange()
    {
        float maxRange = 0f;
        
        // Check legacy autonomous skills
        foreach (var autonomous in activeAutonomousSkills)
        {
            if (autonomous.range > maxRange)
                maxRange = autonomous.range;
        }
        
        // Check optimized autonomous skills
        foreach (var skill in optimizedAutonomousSkills)
        {
            if (skill.range > maxRange)
                maxRange = skill.range;
        }
        
        return maxRange;
    }

    /// <summary>
    /// Updates autonomous skill state machine for improved performance and target caching
    /// </summary>
    private void UpdateAutonomousSkillState(ref OptimizedAutonomousSkill skill)
    {
        if (_cachedFrameTime < skill.nextStateTime)
            return;

        switch (skill.state)
        {
            case AutonomousState.Scanning:
                UpdateScanningState(ref skill);
                break;
            case AutonomousState.Targeting:
                UpdateTargetingState(ref skill);
                break;
            case AutonomousState.Casting:
                UpdateCastingState(ref skill);
                break;
            case AutonomousState.Cooldown:
                UpdateCooldownState(ref skill);
                break;
        }
    }

    /// <summary>
    /// Scanning state - look for enemies within range
    /// </summary>
    private void UpdateScanningState(ref OptimizedAutonomousSkill skill)
    {
        var target = FilterEnemiesByRange(skill.range);
        if (target != null)
        {
            skill.cachedTarget = target;
            skill.state = AutonomousState.Targeting;
            skill.nextStateTime = _cachedFrameTime; // Immediately validate target
            skill.lastTargetValidationTime = _cachedFrameTime;

            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Skill {skill.slotIndex}: Found target, transitioning to Targeting");
            }
        }
        else
        {
            // No target found, check again after update interval
            skill.nextStateTime = _cachedFrameTime + skill.updateInterval;
        }
    }

    /// <summary>
    /// Targeting state - validate cached target before casting
    /// </summary>
    private void UpdateTargetingState(ref OptimizedAutonomousSkill skill)
    {
        // Check if cached target is still valid
        if (skill.cachedTarget == null || !skill.cachedTarget.IsActive)
        {
            // Target invalid, return to scanning
            skill.cachedTarget = null;
            skill.state = AutonomousState.Scanning;
            skill.nextStateTime = _cachedFrameTime;
            
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Skill {skill.slotIndex}: Target invalid, returning to Scanning");
            }
            return;
        }

        // Validate target health and range
        if (!PoolManager.Instance.GetCachedComponent<CombatantHealth>(skill.cachedTarget.GameObject, out var health) || !health.IsAlive)
        {
            skill.cachedTarget = null;
            skill.state = AutonomousState.Scanning;
            skill.nextStateTime = _cachedFrameTime;
            return;
        }

        // Check if target is still in range
        Vector2 playerPos = PlayerManager.PlayerPosition;
        float distanceSqr = (playerPos - (Vector2)skill.cachedTarget.Position).sqrMagnitude;
        float rangeSqr = skill.range * skill.range;

        if (distanceSqr > rangeSqr)
        {
            // Target out of range, return to scanning
            skill.cachedTarget = null;
            skill.state = AutonomousState.Scanning;
            skill.nextStateTime = _cachedFrameTime;
            return;
        }

        // Target is valid, proceed to casting
        skill.state = AutonomousState.Casting;
        skill.nextStateTime = _cachedFrameTime; // Cast immediately
    }

    /// <summary>
    /// Casting state - execute skill at cached target
    /// </summary>
    private void UpdateCastingState(ref OptimizedAutonomousSkill skill)
    {
        // Final validation before casting
        if (skill.cachedTarget == null || !skill.cachedTarget.IsActive)
        {
            skill.cachedTarget = null;
            skill.state = AutonomousState.Scanning;
            skill.nextStateTime = _cachedFrameTime;
            return;
        }

        // Check if skill is on cooldown
        if (IsSkillOnCooldown(skill.slotIndex))
        {
            skill.state = AutonomousState.Cooldown;
            skill.nextStateTime = _cachedFrameTime + GetRemainingCooldown(skill.slotIndex);
            return;
        }

        // Check mana cost
        float manaCost = skill.controller.CalculateFinalManaCost();
        if (PlayerManager.PlayerStats.currentMana < manaCost)
        {
            // Not enough mana, delay next attempt
            skill.nextStateTime = _cachedFrameTime + 0.5f; // Wait 0.5s before trying again
            return;
        }

        // Execute skill at cached target position
        ExecuteAutonomousSkill(ConvertToLegacy(skill), skill.cachedTarget.Position);

        // Transition to cooldown state
        skill.state = AutonomousState.Cooldown;
        float cooldown = CalculateSkillCooldown(skill.controller);
        skill.nextStateTime = _cachedFrameTime + cooldown;
        skill.cachedTarget = null; // Clear target after casting

        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] Skill {skill.slotIndex}: Cast completed, cooldown for {cooldown:F2}s");
        }
    }

    /// <summary>
    /// Cooldown state - wait for skill cooldown to complete
    /// </summary>
    private void UpdateCooldownState(ref OptimizedAutonomousSkill skill)
    {
        // Check if cooldown is over
        if (!IsSkillOnCooldown(skill.slotIndex))
        {
            // Cooldown complete, return to scanning
            skill.state = AutonomousState.Scanning;
            skill.nextStateTime = _cachedFrameTime;
            
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Skill {skill.slotIndex}: Cooldown complete, returning to Scanning");
            }
        }
        // else: still on cooldown, nextStateTime should already be set correctly
    }

    /// <summary>
    /// Converts OptimizedAutonomousSkill to legacy AutonomousSkillData for compatibility
    /// </summary>
    private AutonomousSkillData ConvertToLegacy(OptimizedAutonomousSkill optimized)
    {
        return new AutonomousSkillData
        {
            slotIndex = optimized.slotIndex,
            controller = optimized.controller,
            skillData = optimized.skillData,
            range = optimized.range,
            updateInterval = optimized.updateInterval,
            lastUpdateTime = _cachedFrameTime,
            isActive = true
        };
    }

    /// <summary>
    /// Calculates skill cooldown with attack speed modifiers
    /// </summary>
    private float CalculateSkillCooldown(GemSocketController controller)
    {
        float cooldown = controller.CalculateFinalCooldown();
        float gemAttackSpeed = controller.CalculateFinalAttackSpeed();
        float playerAttackSpeed = PlayerManager.PlayerStats?.GetCalculatedStat(StatType.AttackSpeed) ?? 1f;
        
        float totalAttackSpeed = gemAttackSpeed * playerAttackSpeed;
        if (totalAttackSpeed > 0)
        {
            cooldown /= totalAttackSpeed;
        }
        
        return cooldown;
    }

    /// <summary>
    /// Ensures that all skills with autonomous support gems are registered even if the player
    /// never manually attempted to cast them. This prevents missing autonomous casts.
    /// </summary>
    private void RefreshAutonomousSkills()
    {
        if (equipmentPanel == null || equipmentPanel.skillGemSlots == null) return;

        int slotCount = equipmentPanel.skillGemSlots.Count;
        for (int slotIndex = 0; slotIndex < slotCount; slotIndex++)
        {
            // Skip if an autonomous entry for this slot already exists
            bool alreadyActive = false;
            for (int i = 0; i < activeAutonomousSkills.Count; i++)
            {
                if (activeAutonomousSkills[i].slotIndex == slotIndex)
                {
                    alreadyActive = true;
                    break;
                }
            }
            if (alreadyActive) continue;

            var controller = GetCachedController(slotIndex);
            if (controller == null || !HasAutonomousSupport(controller, slotIndex))
                continue;

            var skillData = controller.skillGemInstance?.gemDataTemplate as SkillGemData;
            if (skillData == null)
                continue;

            CheckAndStartAutonomous(slotIndex, controller, skillData);
        }
    }

    private bool IsSkillStillAutonomous(AutonomousSkillData autonomous)
    {
        if (equipmentPanel == null) return false;

        // Use cached controller with proper cache validation
        var controller = GetCachedController(autonomous.slotIndex);
        return controller != null && HasAutonomousSupport(controller, autonomous.slotIndex);
    }

    private void TryAutonomousCast(AutonomousSkillData autonomous, Vector2 playerPos)
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] TryAutonomousCast called for slot {autonomous.slotIndex}");
        }

        // Check if skill is on cooldown
        if (IsSkillOnCooldown(autonomous.slotIndex))
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous cast skipped for slot {autonomous.slotIndex}: On cooldown");
            }
            return;
        }

        // Check mana cost
        float manaCost = autonomous.controller.CalculateFinalManaCost();
        if (PlayerManager.PlayerStats.currentMana < manaCost)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous cast skipped for slot {autonomous.slotIndex}: Not enough mana ({PlayerManager.PlayerStats.currentMana:F1}/{manaCost:F1})");
            }
            return;
        }

        // Find nearest enemy in range
        var nearestEnemy = FindNearestEnemyInRange(playerPos, autonomous.range);
        if (nearestEnemy == null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous cast skipped for slot {autonomous.slotIndex}: No enemies in range ({autonomous.range:F1})");
            }
            return;
        }

        if (enableDebugLogging)
        {
            float distance = Vector2.Distance(playerPos, nearestEnemy.Position);
            Debug.Log($"[SkillExecutor] Autonomous casting slot {autonomous.slotIndex} at enemy (distance: {distance:F1})");
        }

        // Execute skill targeting the enemy
        ExecuteAutonomousSkill(autonomous, nearestEnemy.Position);
    }

    /// <summary>
    /// Optimized autonomous cast using shared enemy detection results.
    /// Filters shared results by skill range instead of separate spatial queries.
    /// </summary>
    private void TryAutonomousCastOptimized(AutonomousSkillData autonomous, Vector2 playerPos)
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] TryAutonomousCastOptimized called for slot {autonomous.slotIndex}");
        }

        // Check if skill is on cooldown
        if (IsSkillOnCooldown(autonomous.slotIndex))
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous cast skipped for slot {autonomous.slotIndex}: On cooldown");
            }
            return;
        }

        // Check mana cost
        float manaCost = autonomous.controller.CalculateFinalManaCost();
        if (PlayerManager.PlayerStats.currentMana < manaCost)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous cast skipped for slot {autonomous.slotIndex}: Not enough mana ({PlayerManager.PlayerStats.currentMana:F1}/{manaCost:F1})");
            }
            return;
        }

        // Filter shared enemy results by skill range instead of new spatial query
        var nearestEnemy = FilterEnemiesByRange(autonomous.range);
        if (nearestEnemy == null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous cast skipped for slot {autonomous.slotIndex}: No enemies in range ({autonomous.range:F1})");
            }
            return;
        }

        if (enableDebugLogging)
        {
            float distance = Vector2.Distance(playerPos, nearestEnemy.Position);
            Debug.Log($"[SkillExecutor] Autonomous casting slot {autonomous.slotIndex} at enemy (distance: {distance:F1})");
        }

        // Execute skill targeting the enemy
        ExecuteAutonomousSkill(autonomous, nearestEnemy.Position);
    }

    /// <summary>
    /// Filters shared enemy detection results by skill range.
    /// Returns nearest valid enemy within the specified range.
    /// </summary>
    private ICollidable FilterEnemiesByRange(float skillRange)
    {
        if (_sharedEnemyDetection.allEnemiesInMaxRange == null || _sharedEnemyDetection.allEnemiesInMaxRange.Count == 0)
            return null;

        Vector2 playerPos = _sharedEnemyDetection.playerPosition;
        ICollidable nearestEnemy = null;
        float nearestSqrDistance = float.MaxValue;
        float skillRangeSqr = skillRange * skillRange;

        foreach (var collider in _sharedEnemyDetection.allEnemiesInMaxRange)
        {
            if (!collider.IsActive) continue;

            // Verify it's actually an enemy (has health component)
            if (!PoolManager.Instance.GetCachedComponent<CombatantHealth>(collider.GameObject, out var health))
                continue;

            if (!health.IsAlive) continue;

            // Check if enemy is within this skill's range
            float sqrDistance = (playerPos - (Vector2)collider.Position).sqrMagnitude;
            if (sqrDistance <= skillRangeSqr && sqrDistance < nearestSqrDistance)
            {
                nearestSqrDistance = sqrDistance;
                nearestEnemy = collider;
            }
        }

        return nearestEnemy;
    }

    private ICollidable FindNearestEnemyInRange(Vector2 playerPos, float range)
    {
        // Use Physics2D to find enemies in range
        Collider2D[] colliders = Physics2D.OverlapCircleAll(playerPos, range);
        
        _nearbyEnemiesCache.Clear();
        foreach (var collider in colliders)
        {
            if (collider.CompareTag("Enemy"))
            {
                // Add enemy to cache - simplified approach
                // Note: This may need adjustment based on ICollidable implementation
            }
        }

        if (enableDebugLogging && _nearbyEnemiesCache.Count > 0)
        {
            Debug.Log($"[SkillExecutor] Found {_nearbyEnemiesCache.Count} potential enemies in range {range:F1}");
        }

        ICollidable nearestEnemy = null;
        float nearestSqrDistance = float.MaxValue;
        int validEnemies = 0;

        foreach (var collider in _nearbyEnemiesCache)
        {
            if (!collider.IsActive) continue;

            // Verify it's actually an enemy (has health component)
            if (!PoolManager.Instance.GetCachedComponent<CombatantHealth>(collider.GameObject, out var health))
                continue;

            if (!health.IsAlive) continue;

            validEnemies++;
            // Use squared distance to avoid expensive square root calculation
            float sqrDistance = (playerPos - (Vector2)collider.Position).sqrMagnitude;
            if (sqrDistance < nearestSqrDistance)
            {
                nearestSqrDistance = sqrDistance;
                nearestEnemy = collider;
            }
        }

        if (enableDebugLogging && validEnemies > 0)
        {
            // Only calculate actual distance for debug logging when needed
            float actualDistance = Mathf.Sqrt(nearestSqrDistance);
            Debug.Log($"[SkillExecutor] Found {validEnemies} valid living enemies, nearest at distance {actualDistance:F1}");
        }

        return nearestEnemy;
    }

    /// <summary>
    /// Executes an autonomous skill using the strategy pattern.
    /// Preserves autonomous-specific behavior including fixed target positioning for spell echo.
    /// Handles mana consumption, cooldowns, and autonomous support gem effects.
    /// </summary>
    /// <param name="autonomous">Autonomous skill data containing controller and targeting info</param>
    /// <param name="targetPosition">Enemy position for targeting</param>
    private void ExecuteAutonomousSkill(AutonomousSkillData autonomous, Vector3 targetPosition)
    {
        // Get fresh controller data instead of using cached one
        var freshController = GetCachedController(autonomous.slotIndex);
        if (freshController == null)
        {
            Debug.LogWarning($"[SkillExecutor] No controller found for autonomous skill in slot {autonomous.slotIndex}");
            return;
        }
        
        // Cache skill execution values with fresh controller
        CacheSkillExecutionValues(freshController, autonomous.slotIndex);

        if (enableDebugLogging)
        {
            Debug.Log($"[SkillExecutor] Autonomous cast: {autonomous.skillData.gemName} at {targetPosition}");
            Debug.Log($"[SkillExecutor] Using fresh controller with {freshController.supportGemInstances?.Count ?? 0} support gems");
        }

        // Execute autonomous skill using strategy pattern
        var executor = _strategyFactory.GetExecutor(autonomous.skillData.skillType);
        if (executor != null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Autonomous strategy execution: {executor.GetType().Name} for {autonomous.skillData.skillType}");
            }
            // Pass isAutonomous=true to preserve autonomous-specific behavior
            executor.Execute(this, autonomous.slotIndex, freshController,
                           autonomous.skillData, targetPosition, true);
        }
        else
        {
            Debug.LogError($"[SkillExecutor] No strategy found for autonomous {autonomous.skillData.skillType}!");
        }

        // Consume mana
        float manaCost = autonomous.controller.CalculateFinalManaCost();
        PlayerManager.PlayerStats.SpendMana(manaCost);

        // Set cooldown
        float cooldown = autonomous.controller.CalculateFinalCooldown();
        float gemAttackSpeed = autonomous.controller.CalculateFinalAttackSpeed();
        float playerAttackSpeed = PlayerManager.PlayerStats?.GetCalculatedStat(StatType.AttackSpeed) ?? 1f;

        float totalAttackSpeed = gemAttackSpeed * playerAttackSpeed;
        if (totalAttackSpeed > 0)
        {
            cooldown /= totalAttackSpeed;
        }
        if (cooldown > 0)
        {
            skillCooldowns[autonomous.slotIndex] = cooldown;
            
            // Debug logging for autonomous skill cooldown
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] AUTONOMOUS cooldown set for slot {autonomous.slotIndex}: " +
                         $"Base: {autonomous.controller.CalculateFinalCooldown():F2}s, " +
                         $"After attack speed ({totalAttackSpeed:F2}x): {cooldown:F2}s");
            }
        }

        // Apply support gem effects for autonomous casting with fresh controller
        ApplySupportGemEffectsForAutonomous(autonomous.slotIndex, freshController, autonomous.skillData, targetPosition);
    }

    /// <summary>
    /// Apply support gem effects that should work with autonomous casting
    /// </summary>
    private void ApplySupportGemEffectsForAutonomous(int slotIndex, GemSocketController controller, SkillGemData skillData, Vector3 targetPosition)
    {
        // Check for spell echo support gems - these should work with autonomous casting
        CheckAndStartSpellEchoForAutonomous(slotIndex, controller, skillData, targetPosition);

        // Note: Other support gem effects (Pierce, Chain, Fork, etc.) are already applied
        // through the ExecuteInstantSkill/ExecuteProjectileSkill methods and GemSocketController calculations
    }

    /// <summary>
    /// Check and start spell echo for autonomous casting with enemy target position
    /// </summary>
    private void CheckAndStartSpellEchoForAutonomous(int slotIndex, GemSocketController controller, SkillGemData skillData, Vector3 targetPosition)
    {
        // Only check for spell echo if the skill has the Spell tag
        if ((skillData.gemTags & GemTag.Spell) == 0)
            return;

        // Get execution cache for this slot
        var executionCache = GetExecutionCache(slotIndex);

        // Use order-independent support gem effects from cache
        if (executionCache.supportGemEffectsValid && executionCache.supportGemEffects.hasSpellEcho)
        {
            var effects = executionCache.supportGemEffects;

            // Create spell echo data for autonomous casting (uses target position instead of mouse)
            var echoData = new AutonomousSpellEchoData
            {
                slotIndex = slotIndex,
                remainingEchoes = effects.spellEchoCount,
                targetPosition = targetPosition, // Use enemy position for autonomous echoes
                controller = controller,
                skillData = skillData,
                echoDelay = effects.spellEchoDelay,
                echoSpreadRadius = effects.spellEchoSpreadRadius
            };

            // Start the autonomous echo coroutine
            var echoCoroutine = StartCoroutine(ProcessAutonomousSpellEcho(echoData));
            activeSpellEchoes.Add(echoCoroutine);

            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Started autonomous spell echo for slot {slotIndex} with {effects.spellEchoCount} echoes targeting {targetPosition}");
            }
        }
    }

    /// <summary>
    /// Cleanup null spell echoes without lambda allocation
    /// </summary>
    private void CleanupNullEchoes()
    {
        _removalIndices.Clear();
        for (int i = 0; i < activeSpellEchoes.Count; i++)
        {
            if (activeSpellEchoes[i] == null)
                _removalIndices.Add(i);
        }

        // Remove in reverse order to maintain indices
        for (int i = _removalIndices.Count - 1; i >= 0; i--)
        {
            activeSpellEchoes.RemoveAt(_removalIndices[i]);
        }
    }

    /// <summary>
    /// Remove autonomous skill by slot index with object pooling
    /// RemoveAt allocations are acceptable here since this only happens during manual equipment changes
    /// </summary>
    private void RemoveAutonomousSkillBySlot(int slotIndex)
    {
        // Remove from legacy autonomous skills list
        for (int i = activeAutonomousSkills.Count - 1; i >= 0; i--)
        {
            if (activeAutonomousSkills[i].slotIndex == slotIndex)
            {
                var skill = activeAutonomousSkills[i];
                activeAutonomousSkills.RemoveAt(i);
                ReturnAutonomousSkillToPool(skill);
                
                if (enableDebugLogging)
                {
                    Debug.Log($"[SkillExecutor] Removed legacy autonomous skill from slot {slotIndex}");
                }
            }
        }
        
        // Remove from optimized autonomous skills list
        for (int i = optimizedAutonomousSkills.Count - 1; i >= 0; i--)
        {
            if (optimizedAutonomousSkills[i].slotIndex == slotIndex)
            {
                var skill = optimizedAutonomousSkills[i];
                optimizedAutonomousSkills.RemoveAt(i);
                ReturnOptimizedSkillToPool(skill);
                
                if (enableDebugLogging)
                {
                    Debug.Log($"[SkillExecutor] Removed optimized autonomous skill from slot {slotIndex}");
                }
            }
        }
    }


    /// <summary>
    /// Zero-GC autonomous support gem detection with caching - bypasses full ProcessSupportGems for performance
    /// Preserves all existing behavior including debug logging and tag compatibility
    /// Implements LRU cache management to prevent unbounded growth
    /// </summary>
    private bool HasAutonomousSupport(GemSocketController controller, int slotIndex)
    {
        // Check cache first - event-driven invalidation ensures accuracy
        if (_cachedAutonomousStatus.TryGetValue(slotIndex, out bool cachedResult))
        {
            // Update LRU order
            UpdateAutonomousCacheLRU(slotIndex);
            return cachedResult;
        }

        // Check if we need to evict old entries before adding new one
        EvictAutonomousCacheIfNeeded();

        // Cache miss - calculate and cache result
        // Direct processing for autonomous detection - don't depend on execution cache
        // since autonomous discovery happens outside of execution context
        var supportGems = GetCachedSupportGems(controller, slotIndex);

        if (enableDebugLogging && supportGems != null)
        {
            Debug.Log($"[SkillExecutor] HasAutonomousSupport slot {slotIndex}: Found {supportGems.Count} compatible support gems");
            foreach (var gem in supportGems)
            {
                if (gem?.gemDataTemplate is SupportGemData supportData)
                {
                    Debug.Log($"[SkillExecutor] Support gem: {supportData.gemName}, addsAutonomous: {supportData.addsAutonomous}");
                }
            }
        }

        bool hasAutonomous = false;

        // Zero-GC autonomous detection - direct iteration without ProcessSupportGems
        // Respects tag compatibility - only checks compatible support gems from cache
        if (supportGems != null && supportGems.Count > 0)
        {
            // Direct iteration - no LINQ, no collections, no allocations
            for (int i = 0; i < supportGems.Count; i++)
            {
                var gem = supportGems[i];
                if (gem?.gemDataTemplate is SupportGemData supportData && supportData.addsAutonomous)
                {
                    hasAutonomous = true;
                    break;
                }
            }
        }

        // Cache the result for future calls
        _cachedAutonomousStatus[slotIndex] = hasAutonomous;
        
        // Add to LRU tracking
        UpdateAutonomousCacheLRU(slotIndex);
        
        return hasAutonomous;
    }

    /// <summary>
    /// Zero-GC autonomous range detection with caching - direct iteration without ProcessSupportGems
    /// Implements LRU cache management to prevent unbounded growth
    /// </summary>
    private float GetAutonomousRange(GemSocketController controller, int slotIndex)
    {
        // Check cache first - event-driven invalidation ensures accuracy
        if (_cachedAutonomousRange.TryGetValue(slotIndex, out float cachedRange))
        {
            // Update LRU order
            UpdateAutonomousCacheLRU(slotIndex);
            return cachedRange;
        }

        // Cache miss - calculate and cache result
        // Direct processing for autonomous range - don't depend on execution cache
        var supportGems = GetCachedSupportGems(controller, slotIndex);

        float autonomousRange = 0f; // Default range if no autonomous support found

        if (supportGems != null && supportGems.Count > 0)
        {
            // Direct iteration to find first autonomous gem - no allocations
            for (int i = 0; i < supportGems.Count; i++)
            {
                var gem = supportGems[i];
                if (gem?.gemDataTemplate is SupportGemData supportData && supportData.addsAutonomous)
                {
                    autonomousRange = supportData.autonomousRange;
                    break;
                }
            }
        }

        // Cache the result for future calls
        _cachedAutonomousRange[slotIndex] = autonomousRange;
        
        // Add to LRU tracking
        UpdateAutonomousCacheLRU(slotIndex);
        
        return autonomousRange;
    }

    /// <summary>
    /// Zero-GC autonomous update interval detection with caching - direct iteration without ProcessSupportGems
    /// Implements LRU cache management to prevent unbounded growth  
    /// </summary>
    private float GetAutonomousUpdateInterval(GemSocketController controller, int slotIndex)
    {
        // Check cache first - event-driven invalidation ensures accuracy
        if (_cachedAutonomousInterval.TryGetValue(slotIndex, out float cachedInterval))
        {
            // Update LRU order
            UpdateAutonomousCacheLRU(slotIndex);
            return cachedInterval;
        }

        // Cache miss - calculate and cache result
        // Direct processing for autonomous update interval - don't depend on execution cache
        var supportGems = GetCachedSupportGems(controller, slotIndex);

        float autonomousInterval = 0f; // Default interval if no autonomous support found

        if (supportGems != null && supportGems.Count > 0)
        {
            // Direct iteration to find first autonomous gem - no allocations
            for (int i = 0; i < supportGems.Count; i++)
            {
                var gem = supportGems[i];
                if (gem?.gemDataTemplate is SupportGemData supportData && supportData.addsAutonomous)
                {
                    autonomousInterval = supportData.autonomousUpdateInterval;
                    break;
                }
            }
        }

        // Cache the result for future calls
        _cachedAutonomousInterval[slotIndex] = autonomousInterval;
        
        // Add to LRU tracking
        UpdateAutonomousCacheLRU(slotIndex);
        
        return autonomousInterval;
    }

    /// <summary>
    /// Invalidate caches for a specific slot (call when equipment changes)
    /// </summary>
    public void InvalidateSlotCache(int slotIndex)
    {
        cachedControllers.Remove(slotIndex);
        _cachedSupportGems.Remove(slotIndex);

        // Invalidate autonomous support caches
        _cachedAutonomousStatus.Remove(slotIndex);
        _cachedAutonomousRange.Remove(slotIndex);
        _cachedAutonomousInterval.Remove(slotIndex);

        // Invalidate execution cache for this slot
        if (_executionCaches.TryGetValue(slotIndex, out var cache))
        {
            // Return pooled effects to pool before invalidating
            if (cache.supportGemEffectsValid)
            {
                cache.supportGemEffects.ReturnToPool();
            }
            _executionCaches.Remove(slotIndex);
        }
    }



    /// <summary>
    /// Public method to log damage from external systems (e.g., projectiles, spells)
    /// </summary>
    /// <param name="skillName">Name of the skill</param>
    /// <param name="finalDamage">Final damage amount</param>
    /// <param name="damageType">Primary damage type</param>
    /// <param name="source">Source of the damage (optional)</param>
    public void LogExternalDamage(string skillName, float finalDamage, DamageType damageType, string source = "")
    {
        DamageLogger.LogDamage(skillName, finalDamage, damageType, source);
    }

    /// <summary>
    /// Check if damage logging is enabled (for external systems to avoid unnecessary calculations)
    /// </summary>
    public bool IsDamageLoggingEnabled => enableDamageLogging;

    /// <summary>
    /// Update all Ruthless hit counters (for equipment-wide changes)
    /// </summary>
    private void UpdateAllRuthlessHitCounters()
    {
        // Update all slots when all equipment changes
        for (int i = 0; i < 6; i++) // Assuming 6 skill slots
        {
            UpdateRuthlessHitCounter(i);
        }
    }

    /// <summary>
    /// Update Ruthless hit counter registration when support gems change
    /// </summary>
    private void UpdateRuthlessHitCounter(int slotIndex)
    {
        if (RuthlessHitCounter.Instance == null) return;

        // Get the current controller for this slot
        var controller = GetCachedController(slotIndex);
        if (controller == null) return;

        // Check if this slot has Ruthless support
        var supportGems = GetCachedSupportGems(controller, slotIndex);
        bool hasRuthlessSupport = false;
        int ruthlessInterval = 3; // Default interval

        foreach (var supportGem in supportGems)
        {
            if (supportGem?.gemDataTemplate is SupportGemData supportData && supportData.addsRuthlessHits)
            {
                hasRuthlessSupport = true;
                ruthlessInterval = supportData.ruthlessHitInterval;
                break;
            }
        }

        if (hasRuthlessSupport)
        {
            RuthlessHitCounter.Instance.RegisterRuthlessSkill(slotIndex, ruthlessInterval);
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Registered Ruthless support for slot {slotIndex} with interval {ruthlessInterval}");
            }
        }
        else
        {
            RuthlessHitCounter.Instance.UnregisterRuthlessSkill(slotIndex);
            if (enableDebugLogging)
            {
                Debug.Log($"[SkillExecutor] Unregistered Ruthless support for slot {slotIndex}");
            }
        }
    }

    /// <summary>
    /// Initialize random vector pool for zero-GC spell echo spread calculations
    /// </summary>
    private static void InitializeRandomVectorPool()
    {
        if (_randomVectorPoolInitialized) return;

        for (int i = 0; i < _randomVectorPool.Length; i++)
        {
            _randomVectorPool[i] = Random.insideUnitCircle;
        }
        _randomVectorPoolInitialized = true;
    }

    /// <summary>
    /// Get a cached random vector from the pool (zero-GC alternative to Random.insideUnitCircle)
    /// </summary>
    private Vector2 GetCachedRandomVector()
    {
        _randomVectorIndex = (_randomVectorIndex + 1) % _randomVectorPool.Length;
        return _randomVectorPool[_randomVectorIndex];
    }

    /// <summary>
    /// Evicts execution cache entries if size exceeds limit using LRU strategy
    /// </summary>
    private void EvictExecutionCacheIfNeeded()
    {
        while (_executionCaches.Count >= MAX_CACHE_SIZE)
        {
            // Remove least recently used entry
            var lruNode = _executionCacheLRU.Last;
            if (lruNode == null) break;
            
            int slotToEvict = lruNode.Value;
            
            // Return pooled effects to pool before eviction
            if (_executionCaches.TryGetValue(slotToEvict, out var cache) && cache.supportGemEffectsValid)
            {
                try
                {
                    cache.supportGemEffects.ReturnToPool();
                }
                catch (System.InvalidOperationException ex)
                {
                    Debug.LogWarning($"[SkillExecutor] Pool return failed during cache eviction for slot {slotToEvict}: {ex.Message}");
                }
            }
            
            // Remove from all tracking structures
            _executionCaches.Remove(slotToEvict);
            _executionCacheNodes.Remove(slotToEvict);
            _executionCacheLRU.RemoveLast();
        }
    }

    /// <summary>
    /// Updates LRU order for execution cache access
    /// </summary>
    private void UpdateExecutionCacheLRU(int slotIndex)
    {
        if (_executionCacheNodes.TryGetValue(slotIndex, out var node))
        {
            _executionCacheLRU.Remove(node);
            var newNode = _executionCacheLRU.AddFirst(slotIndex);
            _executionCacheNodes[slotIndex] = newNode;
        }
    }

    /// <summary>
    /// Evicts support gems cache entries if size exceeds limit using LRU strategy
    /// </summary>
    private void EvictSupportGemsCacheIfNeeded()
    {
        while (_cachedSupportGems.Count >= MAX_SUPPORT_GEMS_CACHE_SIZE)
        {
            // Remove least recently used entry
            var lruNode = _supportGemsCacheLRU.Last;
            if (lruNode == null) break;
            
            int slotToEvict = lruNode.Value;
            
            // Remove from all tracking structures
            _cachedSupportGems.Remove(slotToEvict);
            _supportGemsCacheNodes.Remove(slotToEvict);
            _supportGemsCacheLRU.RemoveLast();
        }
    }

    /// <summary>
    /// Updates LRU order for support gems cache access
    /// </summary>
    private void UpdateSupportGemsCacheLRU(int slotIndex)
    {
        if (_supportGemsCacheNodes.TryGetValue(slotIndex, out var node))
        {
            _supportGemsCacheLRU.Remove(node);
            var newNode = _supportGemsCacheLRU.AddFirst(slotIndex);
            _supportGemsCacheNodes[slotIndex] = newNode;
        }
    }

    /// <summary>
    /// Evicts autonomous cache entries if size exceeds limit using LRU strategy
    /// </summary>
    private void EvictAutonomousCacheIfNeeded()
    {
        while (_cachedAutonomousStatus.Count >= MAX_AUTONOMOUS_CACHE_SIZE)
        {
            // Remove least recently used entry
            var lruNode = _autonomousCacheLRU.Last;
            if (lruNode == null) break;
            
            int slotToEvict = lruNode.Value;
            
            // Remove from all tracking structures
            _cachedAutonomousStatus.Remove(slotToEvict);
            _cachedAutonomousRange.Remove(slotToEvict);
            _cachedAutonomousInterval.Remove(slotToEvict);
            _autonomousCacheNodes.Remove(slotToEvict);
            _autonomousCacheLRU.RemoveLast();
        }
    }

    /// <summary>
    /// Updates LRU order for autonomous cache access
    /// </summary>
    private void UpdateAutonomousCacheLRU(int slotIndex)
    {
        if (_autonomousCacheNodes.TryGetValue(slotIndex, out var node))
        {
            _autonomousCacheLRU.Remove(node);
            var newNode = _autonomousCacheLRU.AddFirst(slotIndex);
            _autonomousCacheNodes[slotIndex] = newNode;
        }
        else
        {
            // Add new entry if not present
            var newNode = _autonomousCacheLRU.AddFirst(slotIndex);
            _autonomousCacheNodes[slotIndex] = newNode;
        }
    }

    /// <summary>
    /// Clear all caches (call when major equipment changes occur)
    /// </summary>
    public void ClearAllCaches()
    {
        cachedControllers.Clear();
        _cachedSupportGems.Clear();

        // Clear autonomous support caches
        _cachedAutonomousStatus.Clear();
        _cachedAutonomousRange.Clear();
        _cachedAutonomousInterval.Clear();

        // Return pooled effects to pool before clearing all execution caches
        foreach (var kvp in _executionCaches)
        {
            var cache = kvp.Value;
            if (cache.supportGemEffectsValid)
            {
                cache.supportGemEffects.ReturnToPool();
            }
        }
        _executionCaches.Clear();
        
        // Return autonomous skill data to pools before clearing
        foreach (var skill in activeAutonomousSkills)
        {
            ReturnAutonomousSkillToPool(skill);
        }
        activeAutonomousSkills.Clear();
        
        foreach (var skill in optimizedAutonomousSkills)
        {
            ReturnOptimizedSkillToPool(skill);
        }
        optimizedAutonomousSkills.Clear();
        
        // Clear LRU tracking
        _executionCacheLRU.Clear();
        _executionCacheNodes.Clear();
        _supportGemsCacheLRU.Clear();
        _supportGemsCacheNodes.Clear();
        _autonomousCacheLRU.Clear();
        _autonomousCacheNodes.Clear();
    }
}

