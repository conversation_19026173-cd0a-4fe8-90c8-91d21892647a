using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;

/// <summary>
/// MagmaBall behavior states
/// </summary>
public enum MagmaBallState
{
    Seeking,   // Looking for and moving toward targets
    Following  // Attached to target, applying ignite stacks
}

[RequireComponent(typeof(Collider2D))]
public class MagmaBall : MonoBehaviour, ISpawnable
{
    [Title("Magma Ball Settings")]
    [SerializeField] private float moveSpeed = 3f;
    [SerializeField] private float lifetime = 5f;
    [SerializeField] private float targetSearchRadius = 8f;
    [SerializeField] private float igniteDuration = 3f;
    [SerializeField] private float igniteStackInterval = 0.5f;
    
    [Title("Visual Effects")]
    [SerializeField] private bool useTrailParticles = true;
    [ShowIf("useTrailParticles")]
    [SerializeField] private ParticleType trailParticleType = ParticleType.FireTrail;
    [ShowIf("useTrailParticles")]
    [SerializeField] private float trailInterval = 0.2f;
    
    [SerializeField] private bool useImpactParticles = true;
    [ShowIf("useImpactParticles")]
    [SerializeField] private ParticleType impactParticleType = ParticleType.SparkImpact;
    [ShowIf("useImpactParticles")]
    [SerializeField] private int impactParticleCount = 8;
    
    // Runtime properties
    public float damage { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Fire;
    public float ailmentChance { get; set; } = 50f; // Default 50% chance to apply ignite
    public int collisionLayer { get; set; } = 10; // PlayerProjectile layer
    
    // Cached components and references
    private Collider2D col2D;
    private Transform casterTransform;
    private Vector2 currentTarget;
    private bool hasTarget;
    private float remainingLifetime;
    private float lastTrailTime;
    private string cachedSourceId;
    private readonly List<ICollidable> nearbyColliders = new List<ICollidable>();
    
    // State management
    private MagmaBallState currentState = MagmaBallState.Seeking;
    private Transform followTarget;
    private StatusEffectManager targetStatusManager;
    private float lastIgniteStackTime;
    
    // Support for gem system
    public SkillGemData skillGemData { get; set; }
    public List<GemInstance> supportGems { get; set; }
    
    // Ultra-performance activity flag for manager
    public bool IsActive { get; private set; }

    // Pre-allocated arrays for zero-GC collision detection
    private readonly Collider2D[] _overlapResults = new Collider2D[16];
    
    private void Awake()
    {
        col2D = GetComponent<Collider2D>();
        
        // Set initial inactive state BEFORE any other calls
        IsActive = false;
    }
    
    private void Start()
    {
        // Register with manager once during pool initialization
        if (MagmaBallManager.Instance != null)
        {
            MagmaBallManager.Instance.RegisterPooledBall(this);
        }
    }
    
    public void Initialize(Vector3 spawnPosition, Transform caster)
    {
        casterTransform = caster;
        transform.position = spawnPosition;
        remainingLifetime = lifetime;
        hasTarget = false;
        lastTrailTime = 0f;
        
        // Reset state
        currentState = MagmaBallState.Seeking;
        followTarget = null;
        targetStatusManager = null;
        lastIgniteStackTime = 0f;
        
        // Cache source ID for status effect identification
        cachedSourceId = $"MagmaBall_{gameObject.GetInstanceID()}";
        
        // Set collision layer dynamically based on who spawned this magma ball
        gameObject.layer = collisionLayer;
        
        // Find initial target
        FindNearestTarget();
        
        // Activate for manager updates
        IsActive = true;
    }
    
    private void FindNearestTarget()
    {
        if (casterTransform == null) return;
        
        // Determine target layer based on our collision layer
        LayerMask targetLayer;
        if (collisionLayer == 10) // PlayerProjectile
        {
            targetLayer = 1 << 9; // Enemy layer
        }
        else if (collisionLayer == 11) // EnemyProjectile
        {
            targetLayer = 1 << 8; // Player layer
        }
        else
        {
            return; // Unknown layer, no targeting
        }
        
        // Use RaycastCollisionHelper to find targets in radius around caster
        var results = RaycastCollisionHelper.GetSharedOverlapResults();
        int hitCount = RaycastCollisionHelper.OverlapCircleNonAlloc(casterTransform.position, targetSearchRadius, results, targetLayer);
        
        float nearestDistance = float.MaxValue;
        Vector3 nearestPosition = Vector3.zero;
        bool foundTarget = false;
        
        for (int i = 0; i < hitCount; i++)
        {
            var collider = results[i];
            if (collider != null && collider.gameObject != null && collider.gameObject.activeInHierarchy)
            {
                float distance = Vector3.Distance(transform.position, collider.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestPosition = collider.transform.position;
                    foundTarget = true;
                }
            }
        }
        
        hasTarget = foundTarget;
        if (hasTarget)
        {
            currentTarget = nearestPosition;
        }
    }
    
    /// <summary>
    /// Ultra-performance movement update called by MagmaBallManager
    /// Zero-GC replacement for MoveTowardsTarget coroutine
    /// </summary>
    public void UpdateMovement(float deltaTime)
    {
        if (!IsActive || remainingLifetime <= 0f) return;
        
        remainingLifetime -= deltaTime;
        
        // Check lifetime and despawn if expired
        if (remainingLifetime <= 0f)
        {
            ReturnToPool();
            return;
        }
        
        // Spawn trail particles
        if (useTrailParticles)
        {
            lastTrailTime += deltaTime;
            if (lastTrailTime >= trailInterval)
            {
                ParticleEffectManager.Instance.SpawnParticle(trailParticleType, transform.position, 1);
                lastTrailTime = 0f;
            }
        }
        
        // Behavior based on current state
        if (currentState == MagmaBallState.Seeking)
        {
            // Seeking mode: Find and move toward targets
            if (!hasTarget || Random.value < 0.1f) // 10% chance per frame to search for new target
            {
                FindNearestTarget();
            }
            
            // Move towards target
            if (hasTarget)
            {
                Vector2 direction = (currentTarget - (Vector2)transform.position).normalized;
                Vector2 movement = direction * moveSpeed * deltaTime;
                transform.position += (Vector3)movement;
            }
        }
        else if (currentState == MagmaBallState.Following)
        {
            // Following mode: Stick to target and apply ignite stacks
            if (followTarget != null && followTarget.gameObject.activeInHierarchy)
            {
                // Follow target directly with higher speed
                Vector2 direction = ((Vector2)followTarget.position - (Vector2)transform.position).normalized;
                Vector2 movement = direction * (moveSpeed * 1.5f) * deltaTime; // 50% faster when following
                transform.position += (Vector3)movement;
                
                // Apply ignite stacks at intervals
                if (Time.time - lastIgniteStackTime >= igniteStackInterval)
                {
                    ApplyIgniteStack();
                    lastIgniteStackTime = Time.time;
                }
            }
            else
            {
                // Target lost, return to seeking mode
                SwitchToSeekingMode();
            }
        }

        // Perform manual collision detection
        PerformCollisionDetection();
    }
    
    // Manual collision detection (replaces Unity Physics2D events)
    private void PerformCollisionDetection()
    {
        // Only handle collisions in seeking mode (avoid collision damage while following)
        if (currentState != MagmaBallState.Seeking) return;

        // Use manual collision detection instead of Unity events
        LayerMask targetMask = GetTargetLayerMask();
        float detectionRadius = GetCollisionRadius();

        // Use pre-allocated array for zero-GC collision detection
        int hitCount = RaycastCollisionHelper.OverlapCircleNonAlloc(transform.position, detectionRadius, _overlapResults, targetMask);

        for (int i = 0; i < hitCount; i++)
        {
            var collider = _overlapResults[i];
            if (collider != null && collider.gameObject != null)
            {
                HandleCollisionWithTarget(collider.gameObject);
            }
        }
    }

    private void HandleCollisionWithTarget(GameObject target)
    {
        // Determine valid targets based on our collision layer
        bool validTarget = false;
        if (collisionLayer == 10 && target.layer == 9) // PlayerProjectile vs Enemy
        {
            validTarget = true; // Player projectiles can hit enemies
        }
        else if (collisionLayer == 11 && target.layer == 8) // EnemyProjectile vs Player
        {
            validTarget = true; // Enemy projectiles can hit player
        }
        
        if (!validTarget) return;
        
        // Apply immediate damage
        var damageInfo = DamageInfo.FromSingleType(
            damage,
            damageType,
            Random.value < critChance,
            critMultiplier,
            "MagmaBall",
            ailmentChance
        );
        
        // Apply damage based on target type
        bool damageApplied = false;
        StatusEffectManager statusManager = null;
        
        // Handle player damage via PlayerManager
        if (target.layer == 8 && PlayerManager.PlayerGameObject == target)
        {
            PlayerManager.DealDamageToPlayer(damageInfo);
            damageApplied = true;

            // Get status manager for following mode
            PoolManager.Instance.GetCachedComponent<StatusEffectManager>(target, out statusManager);
        }
        // Handle enemy damage
        else if (target.layer == 9)
        {
            IDamageable damageable = null;
            
            // Try CombatantHealth first (for enemies)
            if (PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
            {
                damageable = combatantHealth;
            }
            // Try HealthComponent as fallback
            else if (PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                damageable = healthComponent;
            }

            if (damageable != null)
            {
                damageable.TakeDamage(damageInfo);
                damageApplied = true;

                // Get status manager for following mode
                PoolManager.Instance.GetCachedComponent<StatusEffectManager>(target, out statusManager);
            }
        }
        
        // Only proceed if damage was successfully applied
        if (!damageApplied) return;
        
        // Apply initial ignite effect
        if (statusManager != null)
        {
            ApplyIgniteToTarget(statusManager);
        }
        
        // Spawn impact particles
        if (useImpactParticles)
        {
            ParticleEffectManager.Instance.SpawnParticle(impactParticleType, transform.position, impactParticleCount);
        }
        
        // Switch to following mode instead of despawning
        SwitchToFollowingMode(target.transform, statusManager);
    }
    
    public void OnSpawn()
    {
        // Reset state when spawned from pool
        hasTarget = false;
        remainingLifetime = lifetime;
        lastTrailTime = 0f;
        
        // Reset state management
        currentState = MagmaBallState.Seeking;
        followTarget = null;
        targetStatusManager = null;
        lastIgniteStackTime = 0f;
        
        // Activate for manager updates
        IsActive = true;
    }
    
    public void OnDespawn()
    {
        // Reset state when returned to pool
        hasTarget = false;
        damage = 0f;
        remainingLifetime = 0f;
        lastTrailTime = 0f;
        
        // Reset state management
        currentState = MagmaBallState.Seeking;
        followTarget = null;
        targetStatusManager = null;
        lastIgniteStackTime = 0f;
        
        // Deactivate for manager updates (replaces coroutine stopping)
        IsActive = false;
        
        // Cleanup references
        casterTransform = null;
        skillGemData = null;
        supportGems = null;
        cachedSourceId = null;
    }
    
    private void ReturnToPool()
    {
        IsActive = false; // Deactivate immediately
        PoolManager.Instance.Despawn(gameObject); // Return to pool
    }
    
    /// <summary>
    /// Applies ignite effect to target with proper chance calculation and zero-GC performance
    /// </summary>
    private void ApplyIgniteToTarget(StatusEffectManager statusManager)
    {
        if (Random.value < (ailmentChance / 100f))
        {
            // Calculate ignite damage as percentage of the main damage (Path of Exile style)
            float igniteDamagePerTick = damage * 0.2f; // 20% of main damage per tick
            var igniteEffect = new IgniteEffect(igniteDamagePerTick, igniteDuration, cachedSourceId);
            statusManager.ApplyStatusEffect(igniteEffect);
        }
    }
    
    /// <summary>
    /// Switch to following mode after hitting a target
    /// </summary>
    private void SwitchToFollowingMode(Transform target, StatusEffectManager statusManager)
    {
        currentState = MagmaBallState.Following;
        followTarget = target;
        targetStatusManager = statusManager;
        lastIgniteStackTime = Time.time;
        
        // Clear seeking target info
        hasTarget = false;
    }
    
    /// <summary>
    /// Switch back to seeking mode if target is lost
    /// </summary>
    private void SwitchToSeekingMode()
    {
        currentState = MagmaBallState.Seeking;
        followTarget = null;
        targetStatusManager = null;
        lastIgniteStackTime = 0f;
        
        // Resume seeking
        FindNearestTarget();
    }
    
    /// <summary>
    /// Apply ignite stack during following mode (guaranteed application, no chance roll)
    /// </summary>
    private void ApplyIgniteStack()
    {
        if (targetStatusManager != null)
        {
            // Calculate ignite damage as percentage of the main damage (Path of Exile style)
            float igniteDamagePerTick = damage * 0.15f; // Slightly lower for stacks (15% vs 20% initial)
            var igniteEffect = new IgniteEffect(igniteDamagePerTick, igniteDuration, cachedSourceId);
            targetStatusManager.ApplyStatusEffect(igniteEffect);
        }
    }

    private LayerMask GetTargetLayerMask()
    {
        // Return the target layer mask based on the collision layer
        if (collisionLayer == 10) // PlayerProjectile
        {
            return 1 << 9; // Enemy layer
        }
        else if (collisionLayer == 11) // EnemyProjectile
        {
            return 1 << 8; // Player layer
        }
        return 0;
    }

    private float GetCollisionRadius()
    {
        if (col2D != null)
        {
            // Get radius from collider bounds
            Bounds bounds = col2D.bounds;
            return Mathf.Max(bounds.extents.x, bounds.extents.y);
        }
        return 0.5f; // Default radius
    }
}