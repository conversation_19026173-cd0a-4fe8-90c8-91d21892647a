using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Central manager for player references. Caches all player components
/// and provides static access to avoid GetComponent calls.
/// </summary>
public class PlayerManager : MonoBehaviour
{
    private static PlayerManager instance;
    
    [Title("Player References")]
    [SerializeField] private Transform playerTransform;
    [SerializeField] private PlayerStats playerStats;
    [SerializeField] private PlayerHealth playerHealth; // New primary health component
    [SerializeField] private HealthComponent legacyHealthComponent; // Keep for compatibility
    [SerializeField] private PlayerController playerController;
    [SerializeField] private PlayerTargetPoint[] targetPoints;
    [SerializeField] private GameObject playerGameObject;
    [SerializeField] private GemManager gemManager;
    
    [Title("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    
    // Static Properties for global access
    public static Transform PlayerTransform => instance?.playerTransform;
    public static PlayerStats PlayerStats => instance?.playerStats;
    public static PlayerHealth PlayerHealth => instance?.playerHealth; // New primary health component
    public static HealthComponent LegacyHealthComponent => instance?.legacyHealthComponent; // Keep for compatibility
    public static PlayerController PlayerController => instance?.playerController;
    public static PlayerTargetPoint[] TargetPoints => instance?.targetPoints;
    public static GameObject PlayerGameObject => instance?.playerGameObject;
    public static Vector3 PlayerPosition => PlayerTransform != null ? PlayerTransform.position : Vector3.zero;
    public static bool IsPlayerAlive => PlayerHealth != null ? PlayerHealth.CurrentHealth > 0 : 
                                      (LegacyHealthComponent != null && LegacyHealthComponent.CurrentHealth > 0);
    public static GemManager GemManager => instance?.gemManager;
    
    /// <summary>
    /// Gets the primary target point for currency and item attraction.
    /// Returns the Center type target with highest priority, or falls back to PlayerPosition.
    /// </summary>
    public static Vector3 GetPrimaryTargetPosition()
    {
        if (instance?.targetPoints != null && instance.targetPoints.Length > 0)
        {
            // Find Center type target points first
            PlayerTargetPoint centerTarget = null;
            int highestPriority = int.MinValue;
            
            foreach (var targetPoint in instance.targetPoints)
            {
                if (targetPoint != null && targetPoint.TargetType == PlayerTargetPoint.TargetPointType.Center)
                {
                    if (targetPoint.Priority > highestPriority)
                    {
                        highestPriority = targetPoint.Priority;
                        centerTarget = targetPoint;
                    }
                }
            }
            
            if (centerTarget != null)
            {
                return centerTarget.GetTargetPosition();
            }
            
            // Fallback to any target point with highest priority
            PlayerTargetPoint fallbackTarget = null;
            highestPriority = int.MinValue;
            
            foreach (var targetPoint in instance.targetPoints)
            {
                if (targetPoint != null && targetPoint.Priority > highestPriority)
                {
                    highestPriority = targetPoint.Priority;
                    fallbackTarget = targetPoint;
                }
            }
            
            if (fallbackTarget != null)
            {
                return fallbackTarget.GetTargetPosition();
            }
        }
        
        // Final fallback to player position
        return PlayerPosition;
    }
    
    // Events for when player changes
    public static System.Action<Transform> OnPlayerChanged;
    public static System.Action OnPlayerDied;
    public static System.Action OnPlayerRespawned;
    
    private static string pendingDamageDebugInfo;
    
    private void Awake()
    {
        // Singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializePlayerReferences();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        // Try to find player if not found in Awake
        if (playerTransform == null)
        {
            FindAndCachePlayer();
        }
        
        // Subscribe to health events if available
        SubscribeToPlayerEvents();
    }
    
    /// <summary>
    /// Find and cache all player components
    /// </summary>
    [Button("Refresh Player References")]
    public void FindAndCachePlayer()
    {
        // Find player by tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        
        if (player != null)
        {
            SetPlayer(player);
        }
        else
        {
            Debug.LogWarning("PlayerManager: No GameObject with 'Player' tag found!");
            ClearPlayerReferences();
        }
    }
    
    /// <summary>
    /// Set the player object and cache all components
    /// </summary>
    public void SetPlayer(GameObject player)
    {
        if (player == null) 
        {
            ClearPlayerReferences();
            return;
        }
        
        // Cache main references
        playerGameObject = player;
        playerTransform = player.transform;
        
        // Cache PlayerStats only if not assigned manually
        if (playerStats == null)
            playerStats = player.GetComponent<PlayerStats>();
        
        // Cache PlayerHealth (new primary) only if not assigned manually
        if (playerHealth == null)
            playerHealth = player.GetComponent<PlayerHealth>();
        
        // Cache legacy health component (for compatibility) only if not assigned manually
        if (legacyHealthComponent == null)
            legacyHealthComponent = player.GetComponent<HealthComponent>();
        
        // Cache player controller only if not assigned manually
        if (playerController == null)
            playerController = player.GetComponent<PlayerController>();
        
        
        // Cache gem manager only if not assigned manually
        if (gemManager == null)
        {
            gemManager = player.GetComponent<GemManager>();
            // If not found on player, try to find it anywhere in scene (following established pattern)
            if (gemManager == null)
                gemManager = FindFirstObjectByType<GemManager>();
        }
        
        // Cache target points only if none were assigned manually
        if (targetPoints == null || targetPoints.Length == 0)
            targetPoints = player.GetComponentsInChildren<PlayerTargetPoint>();
        
        // Subscribe to events
        SubscribeToPlayerEvents();
        
        // Notify systems that player changed
        OnPlayerChanged?.Invoke(playerTransform);
        
        if (showDebugInfo)
        {
            Debug.Log($"PlayerManager: Player set to {player.name}");
            Debug.Log($"  - PlayerStats: {(playerStats != null ? "Found" : "Not found")}");
            Debug.Log($"  - PlayerHealth: {(playerHealth != null ? "Found" : "Not found")}");
            Debug.Log($"  - LegacyHealthComponent: {(legacyHealthComponent != null ? "Found" : "Not found")}");
            Debug.Log($"  - PlayerController: {(playerController != null ? "Found" : "Not found")}");
            Debug.Log($"  - GemManager: {(gemManager != null ? "Found" : "Not found")}");
            Debug.Log($"  - Target Points: {targetPoints?.Length ?? 0}");
        }
    }
    
    /// <summary>
    /// Clear all player references
    /// </summary>
    private void ClearPlayerReferences()
    {
        UnsubscribeFromPlayerEvents();
        
        playerGameObject = null;
        playerTransform = null;
        playerStats = null;
        playerHealth = null;
        legacyHealthComponent = null;
        playerController = null;
        gemManager = null;
        targetPoints = null;
    }
    
    /// <summary>
    /// Initialize player references at startup
    /// </summary>
    private void InitializePlayerReferences()
    {
        // If the essential references are already assigned via Inspector, use them.
        if (playerTransform != null)
        {
            // Ensure cached GameObject reference
            if (playerGameObject == null)
                playerGameObject = playerTransform.gameObject;

            // Fallback-cache components if they were not assigned manually
            if (playerStats == null)
                playerStats = playerGameObject.GetComponent<PlayerStats>();

            if (playerHealth == null)
                playerHealth = playerGameObject.GetComponent<PlayerHealth>();

            if (legacyHealthComponent == null)
                legacyHealthComponent = playerGameObject.GetComponent<HealthComponent>();

            if (playerController == null)
                playerController = playerGameObject.GetComponent<PlayerController>();


            if (gemManager == null)
            {
                gemManager = playerGameObject.GetComponent<GemManager>();
                // If not found on player, try to find it anywhere in scene (following established pattern)
                if (gemManager == null)
                    gemManager = FindFirstObjectByType<GemManager>();
            }

            if (targetPoints == null || targetPoints.Length == 0)
                targetPoints = playerGameObject.GetComponentsInChildren<PlayerTargetPoint>();

            // Subscribe to events using whatever references we have
            SubscribeToPlayerEvents();
        }
        else
        {
            // Fall back to automatic discovery
            FindAndCachePlayer();
        }
    }
    
    /// <summary>
    /// Subscribe to player health events
    /// </summary>
    private void SubscribeToPlayerEvents()
    {
        UnsubscribeFromPlayerEvents(); // Prevent double subscription
        
        // Subscribe to PlayerHealth events (primary)
        if (playerHealth != null)
        {
            playerHealth.OnHealthChanged.AddListener(HandleHealthChanged);
            playerHealth.OnDeath.AddListener(HandlePlayerDeath);
        }
        // Fallback to PlayerStats events
        else if (playerStats != null)
        {
            // Note: PlayerStats no longer has health events, this is legacy
        }
        // Fallback to legacy HealthComponent
        else if (legacyHealthComponent != null)
        {
            legacyHealthComponent.OnDeath.AddListener(HandlePlayerDeath);
        }
    }
    
    /// <summary>
    /// Unsubscribe from player health events
    /// </summary>
    private void UnsubscribeFromPlayerEvents()
    {
        if (playerHealth != null)
        {
            playerHealth.OnHealthChanged.RemoveListener(HandleHealthChanged);
            playerHealth.OnDeath.RemoveListener(HandlePlayerDeath);
        }
        
        if (legacyHealthComponent != null)
        {
            legacyHealthComponent.OnDeath.RemoveListener(HandlePlayerDeath);
        }
    }
    
    /// <summary>
    /// Handle health changed from PlayerStats
    /// </summary>
    private void HandleHealthChanged(float currentHealth, float maxHealth)
    {
        if (currentHealth <= 0)
        {
            HandlePlayerDeath();
        }
    }
    
    /// <summary>
    /// Handle player death
    /// </summary>
    private void HandlePlayerDeath()
    {
        OnPlayerDied?.Invoke();
        
        if (showDebugInfo)
        {
            Debug.Log("PlayerManager: Player died");
        }
    }
    
    /// <summary>
    /// Call this when player respawns
    /// </summary>
    public void NotifyPlayerRespawned()
    {
        // Call PlayerHealth respawn method if available
        if (playerHealth != null)
        {
            playerHealth.OnPlayerRespawn();
        }
        
        OnPlayerRespawned?.Invoke();
        
        if (showDebugInfo)
        {
            Debug.Log("PlayerManager: Player respawned");
        }
    }
    
    
    /// <summary>
    /// Deal damage to player with DamageInfo
    /// </summary>
    public static bool DealDamageToPlayer(DamageInfo damageInfo)
    {
        if (instance == null) return false;
        
        // Try PlayerHealth first (preferred)
        if (PlayerHealth != null)
        {
            PlayerHealth.TakeDamage(damageInfo);
            return true;
        }
        // Fallback to legacy HealthComponent
        else if (LegacyHealthComponent != null)
        {
            LegacyHealthComponent.TakeDamage(damageInfo);
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Get current player health
    /// </summary>
    public static float GetPlayerHealth()
    {
        if (PlayerHealth != null)
            return PlayerHealth.CurrentHealth;
        else if (LegacyHealthComponent != null)
            return LegacyHealthComponent.CurrentHealth;
        return 0f;
    }
    
    /// <summary>
    /// Get max player health
    /// </summary>
    public static float GetPlayerMaxHealth()
    {
        if (PlayerHealth != null)
            return PlayerHealth.MaxHealth;
        else if (LegacyHealthComponent != null)
            return LegacyHealthComponent.MaxHealth;
        return 0f;
    }
    
    /// <summary>
    /// Heal the player
    /// </summary>
    public static void HealPlayer(float amount)
    {
        if (PlayerHealth != null)
            PlayerHealth.Heal(amount);
        else if (LegacyHealthComponent != null)
            LegacyHealthComponent.Heal(amount);
    }
    
    /// <summary>
    /// Get the best target point for an enemy at given position
    /// </summary>
    public static PlayerTargetPoint GetBestTargetPoint(Vector3 enemyPosition, TargetSelectionMode selectionMode = TargetSelectionMode.Nearest)
    {
        if (instance == null || TargetPoints == null || TargetPoints.Length == 0) 
            return null;
        
        switch (selectionMode)
        {
            case TargetSelectionMode.Nearest:
                return GetNearestTargetPoint(enemyPosition);
                
            case TargetSelectionMode.HighestPriority:
                return GetHighestPriorityTargetPoint();
                
            default:
                return TargetPoints[0];
        }
    }
    
    private static PlayerTargetPoint GetNearestTargetPoint(Vector3 position)
    {
        PlayerTargetPoint nearest = null;
        float nearestDistance = float.MaxValue;
        
        foreach (var point in TargetPoints)
        {
            if (point == null) continue;
            
            float distance = Vector3.Distance(position, point.GetTargetPosition());
            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearest = point;
            }
        }
        
        return nearest;
    }
    
    private static PlayerTargetPoint GetHighestPriorityTargetPoint()
    {
        PlayerTargetPoint highest = null;
        int highestPriority = int.MinValue;
        
        foreach (var point in TargetPoints)
        {
            if (point == null) continue;
            
            if (point.Priority > highestPriority)
            {
                highestPriority = point.Priority;
                highest = point;
            }
        }
        
        return highest;
    }
    
    private void OnDestroy()
    {
        UnsubscribeFromPlayerEvents();
        
        if (instance == this)
        {
            instance = null;
        }
    }
    
    // Debug helpers
    [Title("Debug")]
    [Button("Log Player Info")]
    private void DebugLogPlayerInfo()
    {
        if (playerTransform != null)
        {
            Debug.Log($"Player: {playerTransform.name} at {playerTransform.position}");
            Debug.Log($"Health: {(IsPlayerAlive ? "Alive" : "Dead")}");
            if (PlayerHealth != null)
                Debug.Log($"  - PlayerHealth: {PlayerHealth.CurrentHealth}/{PlayerHealth.MaxHealth}");
            else if (LegacyHealthComponent != null)
                Debug.Log($"  - LegacyHealthComponent: {LegacyHealthComponent.CurrentHealth}/{LegacyHealthComponent.MaxHealth}");
            Debug.Log($"Target Points: {TargetPoints?.Length ?? 0}");
        }
        else
        {
            Debug.Log("No player found!");
        }
    }
    
    [Button("Test Damage Player")]
    private void DebugDamagePlayer()
    {
        if (Application.isPlaying)
        {
            bool success = DealDamageToPlayer(DamageInfo.FromSingleType(10f, DamageType.Physical, false, 1f, "Debug"));
            Debug.Log($"Damage dealt: {success}");
        }
    }
    
    [Button("Manually Assign Player Stats")]
    private void ManuallyAssignPlayerStats()
    {
        if (!Application.isPlaying) return;
        
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            playerStats = player.GetComponent<PlayerStats>();
            if (playerStats != null)
            {
                Debug.Log($"Successfully manually assigned PlayerStats from {player.name}");
            }
            else
            {
                Debug.LogError($"Could not find PlayerStats component on {player.name}");
            }
        }
    }
    
    [Button("Debug Find PlayerStats")]
    private void DebugFindPlayerStats()
    {
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            Debug.Log($"Found player GameObject: {player.name}");
            
            // Try to find PlayerStats in different ways
            var stats1 = player.GetComponent<PlayerStats>();
            Debug.Log($"GetComponent<PlayerStats>(): {(stats1 != null ? "Found" : "Not found")}");
            
            var stats2 = player.GetComponent<PlayerStats>();
            Debug.Log($"GetComponent<RogueLike.PlayerStats>(): {(stats2 != null ? "Found" : "Not found")}");
            
            // Try with typeof
            var stats3 = player.GetComponent(typeof(PlayerStats)) as PlayerStats;
            Debug.Log($"GetComponent(typeof(RogueLike.PlayerStats)): {(stats3 != null ? "Found" : "Not found")}");
            
            // List all components
            Component[] components = player.GetComponents<Component>();
            Debug.Log($"Total components on player: {components.Length}");
            foreach (var comp in components)
            {
                if (comp != null)
                {
                    Debug.Log($"  - {comp.GetType().FullName}");
                    // Check if it's PlayerStats by type name
                    if (comp.GetType().Name == "PlayerStats")
                    {
                        Debug.Log($"    ^ This is PlayerStats! Namespace: {comp.GetType().Namespace}");
                    }
                }
            }
            
            // Also check children
            Debug.Log("\nChecking children for PlayerStats:");
            var statsInChildren = player.GetComponentsInChildren<Component>();
            foreach (var comp in statsInChildren)
            {
                if (comp != null && comp.GetType().Name == "PlayerStats")
                {
                    Debug.Log($"  Found PlayerStats on child: {comp.gameObject.name}");
                }
            }
        }
        else
        {
            Debug.Log("No GameObject with 'Player' tag found!");
        }
    }
    
    public static void SetPendingDamageDebug(string info)
    {
        pendingDamageDebugInfo = info;
    }
}

/// <summary>
/// Target selection modes for enemies
/// </summary>
public enum TargetSelectionMode
{
    Nearest,
    HighestPriority,
    PreferType,
    Random
}