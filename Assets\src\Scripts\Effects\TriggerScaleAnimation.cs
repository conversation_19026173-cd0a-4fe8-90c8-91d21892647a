using UnityEngine;
using Sirenix.OdinInspector;
using PrimeTween;

public class TriggerScaleAnimation : MonoBehaviour
{
    [Title("Alpha Cutoff Animation Settings")]
    [Tooltip("Shader property name for alpha cutoff (e.g. _Cutoff, _AlphaClipThreshold)")]
    public string cutoffProperty = "_AlphaCutoffValue";

    [MinVal<PERSON>(0f), MaxValue(1f)]
    public float startCutoff = 1f;

    [MinValue(0f), MaxValue(1f)]
    public float endCutoff = 0f;

    [MinValue(0.01f)]
    public float animationDuration = 0.5f;

    [EnumToggleButtons]
    public Ease animationEase = Ease.OutBack;

    // Entfernte Animation Options (reverseOnExit, exitDuration, exitEase)

    [Title("Player Detection")]
    [Tooltip("Collision layer for player detection")]
    public CollisionLayers playerLayer = CollisionLayers.Player;

    [Title("Advanced Settings")]
    [Tooltip("Use unscaled time for the animation")]
    public bool useUnscaledTime = true;

    [Tooltip("Delay before starting the animation")]
    [MinValue(0f)]
    public float startDelay = 0f;

    [Title("Component References")]

    [Required]
    [Tooltip("Renderer component for material access")]
    public Renderer targetRenderer;

    private float originalCutoff;
    private Tween currentTween;
    private Material materialInstance;
    private int cutoffID;

    private void Awake()
    {
        
        if (targetRenderer == null)
        {
            Debug.LogError($"TriggerScaleAnimation on '{gameObject.name}': Renderer reference is missing!");
            return;
        }
        
        materialInstance = targetRenderer.material; // Instance for this object
        cutoffID = Shader.PropertyToID(cutoffProperty);
        originalCutoff = materialInstance.HasProperty(cutoffID) ? materialInstance.GetFloat(cutoffID) : startCutoff;
        
    }

    private void OnDisable()
    {
        currentTween.Stop();
        materialInstance.SetFloat(cutoffID, originalCutoff);
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            AnimateCutoff();
            GetComponent<Collider2D>().enabled = false;
        }
    }

    private void AnimateCutoff()
    {
        currentTween.Stop();
        if (startDelay > 0f)
        {
            currentTween = Tween.Delay(startDelay, useUnscaledTime: useUnscaledTime)
                .OnComplete(() =>
                {
                    currentTween = Tween.Custom(materialInstance, startCutoff, endCutoff, animationDuration,
                        ease: animationEase,
                        onValueChange: (mat, val) => mat.SetFloat(cutoffID, val));
                });
        }
        else
        {
            currentTween = Tween.Custom(materialInstance, startCutoff, endCutoff, animationDuration,
                ease: animationEase,
                onValueChange: (mat, val) => mat.SetFloat(cutoffID, val));
        }
    }

    [Button("Preview Enter Animation", ButtonSizes.Medium)]
    [PropertySpace(SpaceBefore = 10)]
    [ShowIf("@UnityEngine.Application.isPlaying")]
    private void PreviewEnterAnimation()
    {
        AnimateCutoff();
    }

    [Button("Reset Scale", ButtonSizes.Small)]
    [ShowIf("@UnityEngine.Application.isPlaying")]
    private void ResetCutoff()
    {
        currentTween.Stop();
        materialInstance.SetFloat(cutoffID, originalCutoff);
    }
}