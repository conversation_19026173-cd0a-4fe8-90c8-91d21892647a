using UnityEngine;

/// <summary>
/// Strategy implementation for projectile-based skills.
/// Handles projectile spawning, angular spread, parallel projectiles, and all support gem effects.
/// Preserves autonomous targeting behavior and maintains zero-GC performance.
/// </summary>
public class ProjectileSkillExecutor : ISkillExecutor
{
    public bool CanExecute(SkillType skillType) => skillType == SkillType.Projectile;

    public void Execute(SkillExecutor skillExecutor, int slotIndex, GemSocketController controller,
                       SkillGemData skillData, Vector3 targetPosition, bool isAutonomous)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        skillExecutor.CacheSkillExecutionValues(controller, slotIndex);

        // Calculate direction using cached vectors
        var cachedDirectionVector = skillExecutor._cachedDirectionVector;
        cachedDirectionVector = (targetPosition - skillExecutor.transform.position).normalized;
        float baseAngle = Mathf.Atan2(cachedDirectionVector.y, cachedDirectionVector.x) * Mathf.Rad2Deg;

        // Get projectile count and spread (these are relatively cheap)
        int projectileCount = controller.GetTotalProjectileCount();
        bool useParallel = controller.UseParallelProjectiles();
        
        // Access execution cache for cached values
        var executionCache = skillExecutor.GetExecutionCache(slotIndex);
        
        // Spawn multiple projectiles
        for (int i = 0; i < projectileCount; i++)
        {
            Vector3 spawnPosition;
            Quaternion rotation;
            Vector2 direction;

            if (useParallel)
            {
                // Parallel projectiles - calculate lateral offset
                float lateralOffset = controller.GetProjectileLateralOffset();
                float totalWidth = (projectileCount - 1) * lateralOffset;
                float offsetAmount = -totalWidth / 2f + (i * lateralOffset);

                // Calculate perpendicular vector for offset using cached vector
                var cachedPerpendicularVector = skillExecutor._cachedPerpendicularVector;
                cachedPerpendicularVector.Set(-cachedDirectionVector.y, cachedDirectionVector.x);
                cachedPerpendicularVector *= offsetAmount;
                spawnPosition = skillExecutor.transform.position + (Vector3)cachedPerpendicularVector;

                // All parallel projectiles use the same angle and direction
                rotation = Quaternion.Euler(0, 0, baseAngle);
                direction = cachedDirectionVector;
            }
            else
            {
                // Angular spread behavior
                float spreadAngle = controller.GetProjectileSpreadAngle();
                float currentAngle;
                
                if (controller.UseRandomSpread())
                {
                    // Random spread within the spread angle - like Spark in PoE
                    float randomOffset = Random.Range(-spreadAngle / 2f, spreadAngle / 2f);
                    currentAngle = baseAngle + randomOffset;
                }
                else
                {
                    // Even distribution (original behavior)
                    float angleStep = projectileCount > 1 ? spreadAngle / (projectileCount - 1) : 0;
                    float startAngle = baseAngle - (spreadAngle / 2f);
                    currentAngle = startAngle + (angleStep * i);
                }

                rotation = Quaternion.Euler(0, 0, currentAngle);
                float radians = currentAngle * Mathf.Deg2Rad;
                // Use cached vector for direction calculation
                var cachedVector2Temp = skillExecutor._cachedVector2Temp;
                cachedVector2Temp.Set(Mathf.Cos(radians), Mathf.Sin(radians));
                direction = cachedVector2Temp;
                spawnPosition = skillExecutor.transform.position;
            }
            
            GameObject projectileObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, rotation);
            if (projectileObj == null) continue;
            
            // Configure projectile using cached values
            if (PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var projectile))
            {
                // Validate cached damage to prevent 0 damage issue
                float finalDamage = executionCache.playerModifiedDamage;
                if (finalDamage <= 0f)
                {
                    // Recalculate if cache is invalid
                    skillExecutor.CacheSkillExecutionValues(controller, slotIndex);
                    executionCache = skillExecutor.GetExecutionCache(slotIndex);
                    finalDamage = executionCache.playerModifiedDamage;
                    
                    if (finalDamage <= 0f)
                    {
                        // Fallback to base damage if still invalid
                        finalDamage = skillData.baseDamage;
                    }
                }

                // Use cached values instead of expensive method calls
                projectile.damage = finalDamage;
                projectile.speed = skillData.projectileSpeed;
                projectile.lifetime = skillData.duration;
                projectile.critChance = executionCache.finalCritChance;
                projectile.critMultiplier = executionCache.finalCritMultiplier;
                projectile.damageType = executionCache.damageBreakdown.GetPredominantType(); // Use converted type!
                projectile.ailmentChance = skillData.ailmentChance;
                
                // Ensure we have a valid damage breakdown
                if (executionCache.damageBreakdown.TotalDamage <= 0f)
                {
                    Debug.LogWarning($"[ProjectileSkillExecutor] Skill '{skillData.gemName}' has invalid damage breakdown! TotalDamage: {executionCache.damageBreakdown.TotalDamage}, FinalDamage: {finalDamage}, BaseDamage: {skillData.baseDamage}. Creating enhanced fallback breakdown.");
                    
                    // Try to preserve support gem effects by recalculating with current support gems
                    var supportGems = controller.GetCompatibleSupportGems();
                    if (supportGems != null && supportGems.Count > 0)
                    {
                        // Recalculate with support gems to preserve conversion and modifiers
                        var effects = SupportGemProcessor.ProcessSupportGemsZeroGC(supportGems);
                        var recalculatedBreakdown = DamageCalculator.Calculate(skillData.baseDamage, skillData.damageType, effects, PlayerManager.PlayerStats);
                        
                        if (recalculatedBreakdown.TotalDamage > 0f)
                        {
                            // Scale the recalculated breakdown to match the final damage
                            float scaleFactor = finalDamage / recalculatedBreakdown.TotalDamage;
                            recalculatedBreakdown.ScaleAllDamage(scaleFactor);
                            projectile.damageBreakdown = recalculatedBreakdown;
                        }
                        else
                        {
                            // Last resort: use predominant type from cache or base type
                            var predominantType = executionCache.damageBreakdown.GetPredominantType();
                            if (predominantType == default) predominantType = skillData.damageType;
                            
                            var fallbackBreakdown = new DamageBreakdown(finalDamage, predominantType);
                            projectile.damageBreakdown = fallbackBreakdown;
                        }
                        
                        // Return pooled effects to prevent memory leaks
                        effects.ReturnToPool();
                    }
                    else
                    {
                        // No support gems: use base damage type
                        var fallbackBreakdown = new DamageBreakdown(finalDamage, skillData.damageType);
                        projectile.damageBreakdown = fallbackBreakdown;
                    }
                }
                else
                {
                    projectile.damageBreakdown = executionCache.damageBreakdown; // Pass full breakdown!
                }

                // Pass gem data for status effect configuration
                projectile.skillGemData = skillData;
                projectile.supportGems = skillExecutor.GetCachedSupportGems(controller, slotIndex);

                // Apply support gem effects using cached values
                if (executionCache.hasPierce)
                {
                    projectile.SetPiercing(true);
                }

                if (executionCache.hasChain)
                {
                    projectile.SetChaining(true, executionCache.chainCount);
                }

                if (executionCache.hasFork)
                {
                    projectile.SetFork(true, executionCache.forkCount, executionCache.forkAngle);
                }

                if (executionCache.hasAreaDamage)
                {
                    projectile.SetAreaDamage(true, executionCache.areaRadius);
                }

                // Initialize projectile with cached damage (pass actual damage value, not multiplier)
                CollisionLayers layer = skillData.projectileLayer; // Use gem's collision layer
                // Convert CollisionLayers enum to Unity layer index using helper method
                int layerIndex = CollisionLayersHelper.ToUnityLayerIndex(layer);
                projectile.Initialize((Vector2)spawnPosition, direction, finalDamage, layerIndex, skillData.projectileSpeed, skillData.duration);
                
                // Apply speed variation AFTER Initialize() to prevent override (only for multi-projectile skills)
                if (skillData.intrinsicProjectileCount > 1 && skillData.projectileSpeedVariation > 0f)
                {
                    float speedVariation = Random.Range(-skillData.projectileSpeedVariation, skillData.projectileSpeedVariation);
                    projectile.speed = Mathf.Max(1f, skillData.projectileSpeed + speedVariation);
                }
                
                // Double-check damage after initialization
                if (projectile.damage <= 0f)
                {
                    Debug.LogError($"[SkillExecutor] Projectile damage is 0 after initialization! Setting to fallback: {skillData.baseDamage}");
                    projectile.damage = skillData.baseDamage;
                }
            }
        }
    }
}