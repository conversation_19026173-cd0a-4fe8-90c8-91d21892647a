using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Debug tool for testing SerpentineProjectile movement issues
/// </summary>
public class SerpentineProjectileDebugger : MonoBehaviour
{
    [Title("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private KeyCode testKey = KeyCode.T;
    
    [Title("Test Configuration")]
    [SerializeField] private GameObject serpentineProjectilePrefab;
    [SerializeField] private float testSpeed = 10f;
    [SerializeField] private float testLifetime = 5f;
    [SerializeField] private Vector2 testDirection = Vector2.right;
    
    [Title("Spawn Settings")]
    [SerializeField] private Transform spawnPoint;
    [SerializeField] private int projectileLayer = 10; // PlayerProjectile
    
    private void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            SpawnTestProjectile();
        }
    }
    
    [Button("Spawn Test Projectile", ButtonSizes.Large)]
    private void SpawnTestProjectile()
    {
        if (serpentineProjectilePrefab == null)
        {
            Debug.LogError("[SerpentineProjectileDebugger] No projectile prefab assigned!");
            return;
        }
        
        Vector3 spawnPosition = spawnPoint != null ? spawnPoint.position : transform.position;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SerpentineProjectileDebugger] Spawning test projectile at {spawnPosition} with direction {testDirection}");
        }
        
        // Spawn projectile using PoolManager if available, otherwise instantiate
        GameObject projectileObj;
        if (PoolManager.Instance != null)
        {
            projectileObj = PoolManager.Instance.Spawn(serpentineProjectilePrefab, spawnPosition, Quaternion.identity);
        }
        else
        {
            projectileObj = Instantiate(serpentineProjectilePrefab, spawnPosition, Quaternion.identity);
        }
        
        if (projectileObj != null)
        {
            var projectile = projectileObj.GetComponent<SerpentineProjectile>();
            if (projectile != null)
            {
                // Initialize the projectile
                projectile.Initialize(spawnPosition, testDirection, 10f, projectileLayer, testSpeed, testLifetime);
                
                if (enableDebugLogging)
                {
                    Debug.Log($"[SerpentineProjectileDebugger] Projectile initialized successfully");
                }
            }
            else
            {
                Debug.LogError("[SerpentineProjectileDebugger] Spawned object does not have SerpentineProjectile component!");
            }
        }
        else
        {
            Debug.LogError("[SerpentineProjectileDebugger] Failed to spawn projectile!");
        }
    }
    
    [Button("Test Multiple Projectiles", ButtonSizes.Medium)]
    private void SpawnMultipleTestProjectiles()
    {
        for (int i = 0; i < 3; i++)
        {
            SpawnTestProjectile();
        }
    }
    
    [Button("Clear All Projectiles", ButtonSizes.Medium)]
    private void ClearAllProjectiles()
    {
        var projectiles = FindObjectsOfType<SerpentineProjectile>();
        foreach (var projectile in projectiles)
        {
            if (Application.isPlaying)
            {
                if (PoolManager.Instance != null)
                {
                    PoolManager.Instance.Despawn(projectile.gameObject);
                }
                else
                {
                    Destroy(projectile.gameObject);
                }
            }
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SerpentineProjectileDebugger] Cleared {projectiles.Length} projectiles");
        }
    }
    
    [Button("Test Enum Values", ButtonSizes.Medium)]
    private void TestEnumValues()
    {
        Debug.Log("=== ENUM VALUE TEST ===");
        Debug.Log($"CollisionLayers.PlayerProjectile: {CollisionLayers.PlayerProjectile} (int: {(int)CollisionLayers.PlayerProjectile})");
        Debug.Log($"CollisionLayers.EnemyProjectile: {CollisionLayers.EnemyProjectile} (int: {(int)CollisionLayers.EnemyProjectile})");
        Debug.Log($"1 << 10 = {1 << 10}");
        Debug.Log($"1 << 11 = {1 << 11}");
        Debug.Log($"PlayerProjectile == (1 << 10): {CollisionLayers.PlayerProjectile == (CollisionLayers)(1 << 10)}");
        Debug.Log($"EnemyProjectile == (1 << 11): {CollisionLayers.EnemyProjectile == (CollisionLayers)(1 << 11)}");

        // Test the conversion
        Debug.Log($"ToUnityLayerIndex(PlayerProjectile): {CollisionLayersHelper.ToUnityLayerIndex(CollisionLayers.PlayerProjectile)}");
        Debug.Log($"ToUnityLayerIndex(EnemyProjectile): {CollisionLayersHelper.ToUnityLayerIndex(CollisionLayers.EnemyProjectile)}");
    }

    [Button("Log Collision System Status", ButtonSizes.Medium)]
    private void LogCollisionSystemStatus()
    {
        Debug.Log("=== COLLISION SYSTEM STATUS ===");

        // Check for collision layers
        Debug.Log($"Player Layer (8): {LayerMask.LayerToName(8)}");
        Debug.Log($"Enemy Layer (9): {LayerMask.LayerToName(9)}");
        Debug.Log($"PlayerProjectile Layer (10): {LayerMask.LayerToName(10)}");
        Debug.Log($"EnemyProjectile Layer (11): {LayerMask.LayerToName(11)}");
        Debug.Log($"Wall Layer (12): {LayerMask.LayerToName(12)}");
        Debug.Log($"Environment Layer (15): {LayerMask.LayerToName(15)}");

        // Check collision matrix
        Debug.Log($"PlayerProjectile (10) can hit Enemy (9): {!Physics2D.GetIgnoreLayerCollision(10, 9)}");
        Debug.Log($"EnemyProjectile (11) can hit Player (8): {!Physics2D.GetIgnoreLayerCollision(11, 8)}");
        Debug.Log($"PlayerProjectile (10) ignores Player (8): {Physics2D.GetIgnoreLayerCollision(10, 8)}");
        Debug.Log($"PlayerProjectile (10) ignores PlayerProjectile (10): {Physics2D.GetIgnoreLayerCollision(10, 10)}");

        // Test CollisionLayersHelper conversion
        Debug.Log("=== LAYER CONVERSION TEST ===");
        Debug.Log($"CollisionLayers.PlayerProjectile enum value: {CollisionLayers.PlayerProjectile} (int: {(int)CollisionLayers.PlayerProjectile})");
        Debug.Log($"CollisionLayers.EnemyProjectile enum value: {CollisionLayers.EnemyProjectile} (int: {(int)CollisionLayers.EnemyProjectile})");
        Debug.Log($"CollisionLayers.Player enum value: {CollisionLayers.Player} (int: {(int)CollisionLayers.Player})");
        Debug.Log($"CollisionLayers.Enemy enum value: {CollisionLayers.Enemy} (int: {(int)CollisionLayers.Enemy})");

        Debug.Log($"CollisionLayers.PlayerProjectile -> Unity Layer: {CollisionLayersHelper.ToUnityLayerIndex(CollisionLayers.PlayerProjectile)}");
        Debug.Log($"CollisionLayers.EnemyProjectile -> Unity Layer: {CollisionLayersHelper.ToUnityLayerIndex(CollisionLayers.EnemyProjectile)}");
        Debug.Log($"CollisionLayers.Player -> Unity Layer: {CollisionLayersHelper.ToUnityLayerIndex(CollisionLayers.Player)}");
        Debug.Log($"CollisionLayers.Enemy -> Unity Layer: {CollisionLayersHelper.ToUnityLayerIndex(CollisionLayers.Enemy)}");
    }

    [Button("Test Self-Collision Prevention", ButtonSizes.Medium)]
    private void TestSelfCollisionPrevention()
    {
        Debug.Log("=== SELF-COLLISION PREVENTION TEST ===");

        if (serpentineProjectilePrefab == null)
        {
            Debug.LogError("No projectile prefab assigned!");
            return;
        }

        Vector3 spawnPosition = spawnPoint != null ? spawnPoint.position : transform.position;

        // Spawn a test projectile
        GameObject projectileObj = Instantiate(serpentineProjectilePrefab, spawnPosition, Quaternion.identity);
        var projectile = projectileObj.GetComponent<SerpentineProjectile>();

        if (projectile != null)
        {
            // Initialize the projectile
            projectile.Initialize(spawnPosition, testDirection, 10f, projectileLayer, testSpeed, testLifetime);

            // Check the layer assignment
            Debug.Log($"Projectile spawned on layer: {projectileObj.layer}");
            Debug.Log($"Expected layer: {projectileLayer}");
            Debug.Log($"Layer assignment success: {projectileObj.layer == projectileLayer}");

            // Test collision detection with self
            var collider = projectile.GetComponent<Collider2D>();
            if (collider != null)
            {
                float radius = projectile.GetCollisionRadius();
                LayerMask mask = projectile.GetCollisionLayerMask();

                Debug.Log($"Collision radius: {radius}");
                Debug.Log($"Collision mask: {mask} (binary: {System.Convert.ToString(mask, 2)})");

                // Test if the new CircleCastCheck ignores self
                bool hitSelf = RaycastCollisionHelper.CircleCastCheck(
                    spawnPosition, radius, testDirection, 0.1f, mask, out RaycastHit2D hit);

                bool hitSelfWithIgnore = RaycastCollisionHelper.CircleCastCheck(
                    spawnPosition, radius, testDirection, 0.1f, mask, collider, out RaycastHit2D hitIgnored);

                Debug.Log($"CircleCastCheck without ignore: {hitSelf} (hit: {(hit.collider != null ? hit.collider.name : "none")})");
                Debug.Log($"CircleCastCheck with ignore: {hitSelfWithIgnore} (hit: {(hitIgnored.collider != null ? hitIgnored.collider.name : "none")})");
            }

            // Clean up
            if (Application.isPlaying)
            {
                Destroy(projectileObj);
            }
        }
    }
    
    private void OnDrawGizmos()
    {
        if (spawnPoint != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(spawnPoint.position, 0.2f);
            
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(spawnPoint.position, testDirection * 2f);
        }
    }
}
