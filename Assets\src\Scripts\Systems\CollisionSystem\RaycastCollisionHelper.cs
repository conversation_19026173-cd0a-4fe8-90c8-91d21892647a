using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Static utility class for manual collision detection using Unity's Raycast system.
/// Provides optimized collision checking methods that work without Rigidbody2D.
/// Used to replace Unity's automatic physics events with manual collision processing.
///
/// NOTE: All methods are frame-rate independent and work correctly when called from Update().
/// Performance consideration: Update() calls may be more frequent than FixedUpdate() calls.
/// </summary>
public static class RaycastCollisionHelper
{
    // Pre-allocated arrays for zero-GC collision detection
    private static readonly Collider2D[] overlapResults = new Collider2D[64];
    private static readonly RaycastHit2D[] raycastResults = new RaycastHit2D[32];
    
    /// <summary>
    /// Check for collision using circle overlap at a specific position.
    /// Efficient for stationary or slow-moving circular collision detection.
    /// </summary>
    /// <param name="center">Center of the circle</param>
    /// <param name="radius">Radius of the circle</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <param name="hit">The first collider that was hit</param>
    /// <returns>True if any collision was detected</returns>
    public static bool CircleOverlapCheck(Vector2 center, float radius, LayerMask layerMask, out Collider2D hit)
    {
        hit = Physics2D.OverlapCircle(center, radius, layerMask);
        return hit != null;
    }
    
    /// <summary>
    /// Get all colliders overlapping a circle using pre-allocated array (zero-GC).
    /// Efficient for area damage and multi-target effects.
    /// </summary>
    /// <param name="center">Center of the circle</param>
    /// <param name="radius">Radius of the circle</param>
    /// <param name="results">Pre-allocated array to store results</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <returns>Number of colliders found</returns>
    public static int OverlapCircleNonAlloc(Vector2 center, float radius, Collider2D[] results, LayerMask layerMask)
    {
        return Physics2D.OverlapCircleNonAlloc(center, radius, results, layerMask);
    }
    
    /// <summary>
    /// Check for collision using circle cast (moving circle collision).
    /// Perfect for fast-moving projectiles and moving objects.
    /// </summary>
    /// <param name="origin">Starting position of the circle</param>
    /// <param name="radius">Radius of the circle</param>
    /// <param name="direction">Direction of movement</param>
    /// <param name="distance">Maximum distance to cast</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <param name="hit">Information about the first collision</param>
    /// <returns>True if collision was detected</returns>
    public static bool CircleCastCheck(Vector2 origin, float radius, Vector2 direction, float distance, LayerMask layerMask, out RaycastHit2D hit)
    {
        hit = Physics2D.CircleCast(origin, radius, direction, distance, layerMask);
        return hit.collider != null;
    }
    
    /// <summary>
    /// Check for collision using box overlap at a specific position.
    /// Useful for rectangular/square collision areas.
    /// </summary>
    /// <param name="center">Center of the box</param>
    /// <param name="size">Size of the box (width, height)</param>
    /// <param name="angle">Rotation angle in degrees</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <param name="hit">The first collider that was hit</param>
    /// <returns>True if any collision was detected</returns>
    public static bool BoxOverlapCheck(Vector2 center, Vector2 size, float angle, LayerMask layerMask, out Collider2D hit)
    {
        hit = Physics2D.OverlapBox(center, size, angle, layerMask);
        return hit != null;
    }
    
    /// <summary>
    /// Check for collision using box cast (moving box collision).
    /// Perfect for beam attacks and rectangular projectiles.
    /// </summary>
    /// <param name="origin">Starting position of the box</param>
    /// <param name="size">Size of the box (width, height)</param>
    /// <param name="angle">Rotation angle in degrees</param>
    /// <param name="direction">Direction of movement</param>
    /// <param name="distance">Maximum distance to cast</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <param name="hit">Information about the first collision</param>
    /// <returns>True if collision was detected</returns>
    public static bool BoxCastCheck(Vector2 origin, Vector2 size, float angle, Vector2 direction, float distance, LayerMask layerMask, out RaycastHit2D hit)
    {
        hit = Physics2D.BoxCast(origin, size, angle, direction, distance, layerMask);
        return hit.collider != null;
    }
    
    /// <summary>
    /// Simple raycast for line collision detection.
    /// Useful for laser beams and instant hit effects.
    /// </summary>
    /// <param name="origin">Starting position of the ray</param>
    /// <param name="direction">Direction of the ray</param>
    /// <param name="distance">Maximum distance of the ray</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <param name="hit">Information about the first collision</param>
    /// <returns>True if collision was detected</returns>
    public static bool RaycastCheck(Vector2 origin, Vector2 direction, float distance, LayerMask layerMask, out RaycastHit2D hit)
    {
        hit = Physics2D.Raycast(origin, direction, distance, layerMask);
        return hit.collider != null;
    }
    
    /// <summary>
    /// Get all colliders along a ray using pre-allocated array (zero-GC).
    /// Useful for piercing projectiles and multi-hit effects.
    /// </summary>
    /// <param name="origin">Starting position of the ray</param>
    /// <param name="direction">Direction of the ray</param>
    /// <param name="distance">Maximum distance of the ray</param>
    /// <param name="results">Pre-allocated array to store results</param>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <returns>Number of hits found</returns>
    public static int RaycastNonAlloc(Vector2 origin, Vector2 direction, float distance, RaycastHit2D[] results, LayerMask layerMask)
    {
        return Physics2D.RaycastNonAlloc(origin, direction, results, distance, layerMask);
    }
    
    /// <summary>
    /// Create a ContactFilter2D for consistent collision filtering.
    /// Helper method to standardize collision layer filtering.
    /// </summary>
    /// <param name="layerMask">Layer mask for filtering</param>
    /// <param name="includeTriggers">Whether to include trigger colliders</param>
    /// <returns>Configured ContactFilter2D</returns>
    public static ContactFilter2D CreateContactFilter(LayerMask layerMask, bool includeTriggers = true)
    {
        ContactFilter2D filter = new ContactFilter2D();
        filter.SetLayerMask(layerMask);
        filter.useTriggers = includeTriggers;
        filter.useLayerMask = true;
        return filter;
    }
    
    /// <summary>
    /// Get shared overlap results array for temporary use.
    /// Use this when you need a temporary array for collision detection.
    /// WARNING: This array is shared - don't store references to it!
    /// </summary>
    /// <returns>Shared pre-allocated array for collision results</returns>
    public static Collider2D[] GetSharedOverlapResults()
    {
        return overlapResults;
    }
    
    /// <summary>
    /// Get shared raycast results array for temporary use.
    /// Use this when you need a temporary array for raycast detection.
    /// WARNING: This array is shared - don't store references to it!
    /// </summary>
    /// <returns>Shared pre-allocated array for raycast results</returns>
    public static RaycastHit2D[] GetSharedRaycastResults()
    {
        return raycastResults;
    }
    
    /// <summary>
    /// Check if a target GameObject has a damageable component.
    /// Helper method for validating collision targets.
    /// </summary>
    /// <param name="target">The GameObject to check</param>
    /// <returns>True if the target can take damage</returns>
    public static bool IsDamageableTarget(GameObject target)
    {
        return target.GetComponent<IDamageable>() != null || 
               target.GetComponent<HealthComponent>() != null ||
               target.GetComponent<CombatantHealth>() != null;
    }
    
    /// <summary>
    /// Get the collision bounds of a Collider2D as a circle.
    /// Useful for approximating complex colliders as circles.
    /// </summary>
    /// <param name="collider">The collider to analyze</param>
    /// <param name="center">Output: center of the collision circle</param>
    /// <param name="radius">Output: radius of the collision circle</param>
    public static void GetColliderAsCircle(Collider2D collider, out Vector2 center, out float radius)
    {
        Bounds bounds = collider.bounds;
        center = bounds.center;
        // Use the maximum extent as radius for conservative collision detection
        radius = Mathf.Max(bounds.extents.x, bounds.extents.y);
    }
}