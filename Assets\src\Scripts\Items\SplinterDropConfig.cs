using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Configuration for splinter drops from enemies.
/// Defines which splinters drop from which enemy types and in what quantities.
/// </summary>
[CreateAssetMenu(fileName = "SplinterDropConfig", menuName = "2D Rogue/Items/Splinter Drop Config")]
public class SplinterDropConfig : ScriptableObject
{
    [Title("Global Drop Settings")]
    [InfoBox("These settings apply to all enemy types unless overridden")]
    
    [SerializeField, Range(0f, 1f)]
    [LabelText("Base Drop Chance")]
    [Tooltip("Base probability (0-1) that an enemy will drop splinters on death")]
    private float baseDropChance = 0.75f;
    
    [SerializeField, Range(0f, 2f)]
    [LabelText("Distance Scaling Factor")]  
    [Tooltip("Multiplier for drop rates based on distance from spawn (0 = no scaling)")]
    private float distanceScalingFactor = 0.1f;
    
    [Space(10)]
    [Title("Enemy Type Configurations")]
    [InfoBox("Configure splinter drops for each enemy type. Empty configurations will use global defaults.")]
    [ListDrawerSettings(ShowIndexLabels = true, DraggableItems = false, Expanded = true)]
    [SerializeField]
    private List<EnemyTypeSplinterConfig> enemyConfigurations = new List<EnemyTypeSplinterConfig>
    {
        // Default configurations - you can customize these in the inspector
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Basic },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Boss },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Fire },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Ice },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Nature },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Lightning },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Chaos },
        new EnemyTypeSplinterConfig { enemyType = EnemyType.Shadow }
    };
    
    #region Public API
    
    /// <summary>
    /// Gets the drop configuration for a specific enemy type
    /// </summary>
    public EnemyTypeSplinterConfig GetConfigForEnemyType(EnemyType enemyType)
    {
        foreach (var config in enemyConfigurations)
        {
            if (config.enemyType == enemyType)
            {
                return config;
            }
        }
        
        // Return default config if not found
        return new EnemyTypeSplinterConfig { enemyType = enemyType };
    }
    
    /// <summary>
    /// Calculates final drop chance based on enemy type and distance scaling
    /// </summary>
    public float CalculateFinalDropChance(EnemyType enemyType, int distanceFromSpawn)
    {
        var config = GetConfigForEnemyType(enemyType);
        float dropChance = config.overrideDropChance >= 0 ? config.overrideDropChance : baseDropChance;
        
        // Apply distance scaling
        if (distanceScalingFactor > 0 && distanceFromSpawn > 0)
        {
            float distanceMultiplier = 1f + (distanceScalingFactor * distanceFromSpawn);
            dropChance *= distanceMultiplier;
        }
        
        return Mathf.Clamp01(dropChance);
    }
    
    /// <summary>
    /// Gets weighted splinter drop for an enemy type
    /// </summary>
    public SplinterDropResult CalculateSplinterDrop(EnemyType enemyType, int distanceFromSpawn)
    {
        var config = GetConfigForEnemyType(enemyType);
        float finalDropChance = CalculateFinalDropChance(enemyType, distanceFromSpawn);
        
        // Check if drop occurs
        if (Random.Range(0f, 1f) > finalDropChance)
        {
            return new SplinterDropResult(); // No drop
        }
        
        // Select splinter type based on weights
        var selectedDrop = SelectWeightedSplinterDrop(config.splinterDrops);
        if (selectedDrop == null)
        {
            return new SplinterDropResult(); // No valid drops configured
        }
        
        // Calculate amount with distance scaling
        int baseAmount = Random.Range(selectedDrop.minAmount, selectedDrop.maxAmount + 1);
        int finalAmount = CalculateScaledAmount(baseAmount, distanceFromSpawn);
        
        return new SplinterDropResult
        {
            splinterType = selectedDrop.splinterType,
            amount = finalAmount,
            hasDropped = true,
            selectedDrop = selectedDrop
        };
    }
    
    /// <summary>
    /// Gets base drop chance for display/debugging
    /// </summary>
    public float GetBaseDropChance() => baseDropChance;
    
    /// <summary>
    /// Gets distance scaling factor for display/debugging  
    /// </summary>
    public float GetDistanceScalingFactor() => distanceScalingFactor;
    
    #endregion
    
    #region Private Methods
    
    private SplinterDrop SelectWeightedSplinterDrop(List<SplinterDrop> drops)
    {
        if (drops == null || drops.Count == 0)
        {
            return null;
        }
        
        // Calculate total weight
        float totalWeight = 0f;
        foreach (var drop in drops)
        {
            if (drop.weight > 0 && drop.splinterType.IsSingleType())
            {
                totalWeight += drop.weight;
            }
        }
        
        if (totalWeight <= 0f)
        {
            return null;
        }
        
        // Select based on weight
        float randomValue = Random.Range(0f, totalWeight);
        float currentWeight = 0f;
        
        foreach (var drop in drops)
        {
            if (drop.weight > 0 && drop.splinterType.IsSingleType())
            {
                currentWeight += drop.weight;
                if (randomValue <= currentWeight)
                {
                    return drop;
                }
            }
        }
        
        // Fallback to first valid drop
        return drops.Find(d => d.weight > 0 && d.splinterType.IsSingleType());
    }
    
    private int CalculateScaledAmount(int baseAmount, int distanceFromSpawn)
    {
        if (distanceScalingFactor <= 0 || distanceFromSpawn <= 0)
        {
            return baseAmount;
        }
        
        // Scale amount slightly based on distance (less aggressive than drop chance scaling)
        float distanceMultiplier = 1f + (distanceScalingFactor * 0.5f * distanceFromSpawn);
        int scaledAmount = Mathf.RoundToInt(baseAmount * distanceMultiplier);
        
        return Mathf.Max(1, scaledAmount); // Ensure at least 1 splinter drops
    }
    
    #endregion
    
    #region Validation
    
    [Button("Validate Configuration")]
    [PropertyOrder(100)]
    private void ValidateConfiguration()
    {
        bool hasErrors = false;
        
        // Check for duplicate enemy types
        var seenTypes = new HashSet<EnemyType>();
        foreach (var config in enemyConfigurations)
        {
            if (seenTypes.Contains(config.enemyType))
            {
                Debug.LogError($"[SplinterDropConfig] Duplicate enemy type: {config.enemyType}", this);
                hasErrors = true;
            }
            seenTypes.Add(config.enemyType);
            
            // Validate splinter drops
            foreach (var drop in config.splinterDrops)
            {
                if (drop.minAmount > drop.maxAmount)
                {
                    Debug.LogError($"[SplinterDropConfig] Invalid amount range for {config.enemyType} - {drop.splinterType}: min({drop.minAmount}) > max({drop.maxAmount})", this);
                    hasErrors = true;
                }
                
                if (drop.weight < 0)
                {
                    Debug.LogError($"[SplinterDropConfig] Negative weight for {config.enemyType} - {drop.splinterType}: {drop.weight}", this);
                    hasErrors = true;
                }
                
                if (drop.prefab == null)
                {
                    Debug.LogError($"[SplinterDropConfig] Missing prefab for {config.enemyType} - {drop.splinterType}", this);
                    hasErrors = true;
                }
                else if (!drop.IsValidPrefab())
                {
                    Debug.LogError($"[SplinterDropConfig] Invalid prefab '{drop.prefab.name}' for {config.enemyType} - {drop.splinterType}: Missing required components (SplinterPickup, SpatialCollider, SpriteRenderer)", this);
                    hasErrors = true;
                }
            }
        }
        
        if (!hasErrors)
        {
            Debug.Log("[SplinterDropConfig] Configuration validation passed ✓", this);
        }
    }
    
    #endregion
}

/// <summary>
/// Configuration for splinter drops from a specific enemy type
/// </summary>
[System.Serializable]
public class EnemyTypeSplinterConfig
{
    [HorizontalGroup("Enemy")]
    [LabelWidth(80)]
    public EnemyType enemyType;
    
    [HorizontalGroup("Enemy")]
    [LabelWidth(100)]
    [Range(-1f, 1f)]
    [InfoBox("Override drop chance for this enemy type. -1 = use global default")]
    public float overrideDropChance = -1f;
    
    [Space(5)]
    [InfoBox("Configure which splinters this enemy type can drop")]
    [ListDrawerSettings(ShowIndexLabels = false, DraggableItems = false)]
    public List<SplinterDrop> splinterDrops = new List<SplinterDrop>
    {
        new SplinterDrop { splinterType = SplinterType.Red, weight = 1f, minAmount = 1, maxAmount = 3 }
    };
    
    [ShowInInspector, ReadOnly]
    [LabelText("Total Drop Weight")]
    private float TotalWeight => splinterDrops?.Sum(d => d.weight) ?? 0f;
}

/// <summary>
/// Individual splinter drop configuration
/// </summary>
[System.Serializable]
public class SplinterDrop
{
    [HorizontalGroup("Type", Width = 80)]
    [HideLabel]
    public SplinterType splinterType = SplinterType.Red;
    
    [HorizontalGroup("Type", Width = 120)]
    [LabelText("Prefab")]
    [LabelWidth(45)]
    [Required]
    [Tooltip("Drag the specific splinter prefab for this type")]
    public GameObject prefab;
    
    [HorizontalGroup("Settings", Width = 60)]  
    [LabelText("Weight")]
    [LabelWidth(45)]
    [Range(0f, 10f)]
    public float weight = 1f;
    
    [HorizontalGroup("Settings", Width = 50)]
    [LabelText("Min")]
    [LabelWidth(30)]
    [Range(1, 25)]
    public int minAmount = 1;
    
    [HorizontalGroup("Settings", Width = 50)]
    [LabelText("Max")]
    [LabelWidth(30)]
    [Range(1, 25)]
    public int maxAmount = 3;
    
    [ShowInInspector, ReadOnly]
    [LabelText("Preview")]
    private string DropPreview => $"{minAmount}-{maxAmount} {splinterType} (Weight: {weight}) → {(prefab ? prefab.name : "NO PREFAB")}";
    
    /// <summary>
    /// Validates that the prefab has required components
    /// </summary>
    public bool IsValidPrefab()
    {
        if (prefab == null) return false;
        
        var pickup = prefab.GetComponent<SplinterPickup>();
        var collider = prefab.GetComponent<Collider2D>();
        var renderer = prefab.GetComponent<SpriteRenderer>();
        
        return pickup != null && collider != null && renderer != null;
    }
}

/// <summary>
/// Result of splinter drop calculation
/// </summary>
public struct SplinterDropResult
{
    public SplinterType splinterType;
    public int amount;
    public bool hasDropped;
    public SplinterDrop selectedDrop; // Contains the prefab and other configuration
    
    public static SplinterDropResult NoDrop => new SplinterDropResult { hasDropped = false };
}