/// <summary>
/// Unity Physics2D layer collision flags for collision detection
/// Configure these layers in Project Settings -> Physics2D -> Layer Collision Matrix
/// </summary>
[System.Flags]
public enum CollisionLayers
{
    None = 0,
    
    // Unity built-in layers (0-7)
    Default = 1 << 0,
    TransparentFx = 1 << 1,
    IgnoreRaycast = 1 << 2,
    Water = 1 << 4,
    UI = 1 << 5,
    
    // Custom game layers (8-31)
    Player = 1 << 8,
    Enemy = 1 << 9,
    PlayerProjectile = 1 << 10,
    EnemyProjectile = 1 << 11,
    Wall = 1 << 12,
    Trigger = 1 << 13,
    Pickup = 1 << 14,
    Environment = 1 << 15,
    Interactable = 1 << 16,
    Vegetation = 1 << 17,
    Breach = 1 << 18,
    Shop = 1 << 19,
    
    // Common layer combinations for backwards compatibility
    AllProjectiles = PlayerProjectile | EnemyProjectile,
    AllCharacters = Player | Enemy,
    AllSolid = Wall | Environment,
    AllInteractable = Trigger | Pickup | Interactable | Shop,
    All = ~0
}

/// <summary>
/// Helper class for layer mask calculations
/// </summary>
public static class CollisionLayersHelper
{
    /// <summary>
    /// Convert CollisionLayers enum to Unity layer index
    /// Handles single flags only (not combinations)
    /// </summary>
    public static int ToUnityLayerIndex(CollisionLayers layer)
    {
        switch (layer)
        {
            case CollisionLayers.Default: return 0;
            case CollisionLayers.TransparentFx: return 1;
            case CollisionLayers.IgnoreRaycast: return 2;
            case CollisionLayers.Water: return 4;
            case CollisionLayers.UI: return 5;
            case CollisionLayers.Player: return 8;
            case CollisionLayers.Enemy: return 9;
            case CollisionLayers.PlayerProjectile: return 10;
            case CollisionLayers.EnemyProjectile: return 11;
            case CollisionLayers.Wall: return 12;
            case CollisionLayers.Trigger: return 13;
            case CollisionLayers.Pickup: return 14;
            case CollisionLayers.Environment: return 15;
            case CollisionLayers.Interactable: return 16;
            case CollisionLayers.Vegetation: return 17;
            case CollisionLayers.Breach: return 18;
            case CollisionLayers.Shop: return 19;
            default:
                // For combined flags, try to extract the first valid single flag
                if ((layer & CollisionLayers.PlayerProjectile) != 0) return 10;
                if ((layer & CollisionLayers.EnemyProjectile) != 0) return 11;
                if ((layer & CollisionLayers.Player) != 0) return 8;
                if ((layer & CollisionLayers.Enemy) != 0) return 9;
                UnityEngine.Debug.LogWarning($"[CollisionLayersHelper] Unknown layer combination: {layer}, defaulting to Enemy layer");
                return 9; // Default to Enemy layer
        }
    }
    /// <summary>
    /// Get layer mask for player collision detection
    /// Player collides with: Enemy, EnemyProjectile, Wall, Trigger, Pickup, Environment, Interactable, Breach, Shop
    /// </summary>
    public static int GetPlayerCollisionMask()
    {
        return (int)(CollisionLayers.Enemy | CollisionLayers.EnemyProjectile | CollisionLayers.Wall | 
                    CollisionLayers.Trigger | CollisionLayers.Pickup | CollisionLayers.Environment | 
                    CollisionLayers.Interactable | CollisionLayers.Breach | CollisionLayers.Shop);
    }
    
    /// <summary>
    /// Get layer mask for enemy collision detection
    /// Enemy collides with: Player, PlayerProjectile, Wall, Environment
    /// </summary>
    public static int GetEnemyCollisionMask()
    {
        return (int)(CollisionLayers.Player | CollisionLayers.PlayerProjectile | CollisionLayers.Wall | CollisionLayers.Environment);
    }
    
    /// <summary>
    /// Get layer mask for player projectile collision detection
    /// PlayerProjectile collides with: Enemy, Wall, Environment
    /// </summary>
    public static int GetPlayerProjectileCollisionMask()
    {
        return (int)(CollisionLayers.Enemy | CollisionLayers.Wall | CollisionLayers.Environment);
    }
    
    /// <summary>
    /// Get layer mask for enemy projectile collision detection
    /// EnemyProjectile collides with: Player, Wall, Environment
    /// </summary>
    public static int GetEnemyProjectileCollisionMask()
    {
        return (int)(CollisionLayers.Player | CollisionLayers.Wall | CollisionLayers.Environment);
    }
    
    /// <summary>
    /// Get layer mask for area effect detection (damage all enemies in area)
    /// </summary>
    public static int GetAreaEffectMask()
    {
        return (int)CollisionLayers.Enemy;
    }
    
    /// <summary>
    /// Get layer mask for trigger detection (player interaction)
    /// </summary>
    public static int GetTriggerMask()
    {
        return (int)CollisionLayers.Player;
    }
}