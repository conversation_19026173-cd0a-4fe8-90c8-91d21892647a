/// <summary>
/// Unity Physics2D layer collision flags for collision detection
/// Configure these layers in Project Settings -> Physics2D -> Layer Collision Matrix
/// </summary>
[System.Flags]
public enum CollisionLayers
{
    None = 0,
    
    // Unity built-in layers (0-7)
    Default = 1 << 0,
    TransparentFx = 1 << 1,
    IgnoreRaycast = 1 << 2,
    Water = 1 << 4,
    UI = 1 << 5,
    
    // Custom game layers (8-31)
    Player = 1 << 8,
    Enemy = 1 << 9,
    PlayerProjectile = 1 << 10,
    EnemyProjectile = 1 << 11,
    Wall = 1 << 12,
    Trigger = 1 << 13,
    Pickup = 1 << 14,
    Environment = 1 << 15,
    Interactable = 1 << 16,
    Vegetation = 1 << 17,
    Breach = 1 << 18,
    Shop = 1 << 19,
    
    // Common layer combinations for backwards compatibility
    AllProjectiles = PlayerProjectile | EnemyProjectile,
    AllCharacters = Player | Enemy,
    AllSolid = Wall | Environment,
    AllInteractable = Trigger | Pickup | Interactable | Shop,
    All = ~0
}

/// <summary>
/// Helper class for layer mask calculations
/// </summary>
public static class CollisionLayersHelper
{
    /// <summary>
    /// Convert CollisionLayers enum to Unity layer index
    /// Handles single flags only (not combinations)
    /// </summary>
    public static int ToUnityLayerIndex(CollisionLayers layer)
    {
        // DEBUG: Log the conversion process
        UnityEngine.Debug.Log($"[CollisionLayersHelper] ToUnityLayerIndex called with: {layer} (int value: {(int)layer})");

        switch (layer)
        {
            case CollisionLayers.Default:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Default, returning 0");
                return 0;
            case CollisionLayers.TransparentFx:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched TransparentFx, returning 1");
                return 1;
            case CollisionLayers.IgnoreRaycast:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched IgnoreRaycast, returning 2");
                return 2;
            case CollisionLayers.Water:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Water, returning 4");
                return 4;
            case CollisionLayers.UI:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched UI, returning 5");
                return 5;
            case CollisionLayers.Player:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Player, returning 8");
                return 8;
            case CollisionLayers.Enemy:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Enemy, returning 9");
                return 9;
            case CollisionLayers.PlayerProjectile:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched PlayerProjectile, returning 10");
                return 10;
            case CollisionLayers.EnemyProjectile:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched EnemyProjectile, returning 11");
                return 11;
            case CollisionLayers.Wall:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Wall, returning 12");
                return 12;
            case CollisionLayers.Trigger:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Trigger, returning 13");
                return 13;
            case CollisionLayers.Pickup:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Pickup, returning 14");
                return 14;
            case CollisionLayers.Environment:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Environment, returning 15");
                return 15;
            case CollisionLayers.Interactable:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Interactable, returning 16");
                return 16;
            case CollisionLayers.Vegetation:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Vegetation, returning 17");
                return 17;
            case CollisionLayers.Breach:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Breach, returning 18");
                return 18;
            case CollisionLayers.Shop:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] Matched Shop, returning 19");
                return 19;
            default:
                UnityEngine.Debug.Log($"[CollisionLayersHelper] No direct match, checking flag combinations...");
                // For combined flags, try to extract the first valid single flag
                if ((layer & CollisionLayers.PlayerProjectile) != 0)
                {
                    UnityEngine.Debug.Log($"[CollisionLayersHelper] Found PlayerProjectile flag, returning 10");
                    return 10;
                }
                if ((layer & CollisionLayers.EnemyProjectile) != 0)
                {
                    UnityEngine.Debug.Log($"[CollisionLayersHelper] Found EnemyProjectile flag, returning 11");
                    return 11;
                }
                if ((layer & CollisionLayers.Player) != 0)
                {
                    UnityEngine.Debug.Log($"[CollisionLayersHelper] Found Player flag, returning 8");
                    return 8;
                }
                if ((layer & CollisionLayers.Enemy) != 0)
                {
                    UnityEngine.Debug.Log($"[CollisionLayersHelper] Found Enemy flag, returning 9");
                    return 9;
                }
                UnityEngine.Debug.LogWarning($"[CollisionLayersHelper] Unknown layer combination: {layer} (int: {(int)layer}), defaulting to Enemy layer");
                return 9; // Default to Enemy layer
        }
    }
    /// <summary>
    /// Get layer mask for player collision detection
    /// Player collides with: Enemy, EnemyProjectile, Wall, Trigger, Pickup, Environment, Interactable, Breach, Shop
    /// </summary>
    public static int GetPlayerCollisionMask()
    {
        return (int)(CollisionLayers.Enemy | CollisionLayers.EnemyProjectile | CollisionLayers.Wall | 
                    CollisionLayers.Trigger | CollisionLayers.Pickup | CollisionLayers.Environment | 
                    CollisionLayers.Interactable | CollisionLayers.Breach | CollisionLayers.Shop);
    }
    
    /// <summary>
    /// Get layer mask for enemy collision detection
    /// Enemy collides with: Player, PlayerProjectile, Wall, Environment
    /// </summary>
    public static int GetEnemyCollisionMask()
    {
        return (int)(CollisionLayers.Player | CollisionLayers.PlayerProjectile | CollisionLayers.Wall | CollisionLayers.Environment);
    }
    
    /// <summary>
    /// Get layer mask for player projectile collision detection
    /// PlayerProjectile collides with: Enemy, Wall, Environment
    /// </summary>
    public static int GetPlayerProjectileCollisionMask()
    {
        return (int)(CollisionLayers.Enemy | CollisionLayers.Wall | CollisionLayers.Environment);
    }
    
    /// <summary>
    /// Get layer mask for enemy projectile collision detection
    /// EnemyProjectile collides with: Player, Wall, Environment
    /// </summary>
    public static int GetEnemyProjectileCollisionMask()
    {
        return (int)(CollisionLayers.Player | CollisionLayers.Wall | CollisionLayers.Environment);
    }
    
    /// <summary>
    /// Get layer mask for area effect detection (damage all enemies in area)
    /// </summary>
    public static int GetAreaEffectMask()
    {
        return (int)CollisionLayers.Enemy;
    }
    
    /// <summary>
    /// Get layer mask for trigger detection (player interaction)
    /// </summary>
    public static int GetTriggerMask()
    {
        return (int)CollisionLayers.Player;
    }
}