using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using PrimeTween;


/// <summary>
/// Generisches Animations-System mit PrimeTween.
/// Unterstützt verschiedene Preset-Animationen für UI-Elemente und Sprites.
/// Funktioniert mit Image, CanvasGroup, SpriteRenderer und Transform components.
/// </summary>
[DisallowMultipleComponent]
public class PrimeTweenEffect : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler
{
    public enum AnimationPreset
    {
        None,
        FadeOnly,
        ScaleIn,
        ScaleBounce,
        ScaleToTarget,
        MaterialProperty,
        Pulse,
        RotateIn
    }

    public enum PointerStayEffect
    {
        None,
        Pulse,          // Kontinuierliches Pulsieren
        Rotate,         // Kontinuierliche Rotation
        Glow            // Glow-Effekt (Scale + Alpha)
    }

    public enum PointerExitEffect
    {
        None,
        FadeOut,        // Alpha reduzieren
        ScaleDown,      // Kleiner skalieren
        RotateOut       // Herausrotieren
    }

    #region Main Animation Settings
    [Title("Animation Preset")]
    [EnumToggleButtons]
    public AnimationPreset animationPreset = AnimationPreset.None;

    [ShowIf("@animationPreset != AnimationPreset.None")]
    [Tooltip("Startet Animation automatisch bei OnEnable")]
    public bool playOnEnable = true;

    [ShowIf("@animationPreset != AnimationPreset.None")]
    [MinValue(0f), Tooltip("Dauer der Animation in Sekunden")]
    public float duration = 0.3f;

    [ShowIf("@animationPreset != AnimationPreset.None && animationPreset != AnimationPreset.Pulse")]
    [EnumToggleButtons, Tooltip("Ease-Typ für die Animation")]
    public Ease ease = Ease.OutQuad;

    [ShowIf("@animationPreset != AnimationPreset.None")]
    [MinValue(0f), Tooltip("Verzögerung vor Animation-Start")]
    public float delay = 0f;
    #endregion

    #region Pulse Settings
    [ShowIf("@animationPreset == AnimationPreset.Pulse")]
    [FoldoutGroup("Pulse Settings")]
    [MinValue(1f), Tooltip("Scale-Multiplikator für Pulse")]
    public float pulseScale = 1.1f;

    [ShowIf("@animationPreset == AnimationPreset.Pulse")]
    [FoldoutGroup("Pulse Settings")]
    [MinValue(-1), Tooltip("Anzahl Wiederholungen (-1 = unendlich)")]
    public int pulseLoops = -1;
    #endregion

    #region Scale To Target Settings
    [ShowIf("@animationPreset == AnimationPreset.ScaleToTarget")]
    [FoldoutGroup("Scale To Target Settings")]
    [Tooltip("Start-Scale für die Animation")]
    public Vector3 startScale = Vector3.zero;

    [ShowIf("@animationPreset == AnimationPreset.ScaleToTarget")]
    [FoldoutGroup("Scale To Target Settings")]
    [Tooltip("Ziel-Scale für die Animation")]
    public Vector3 targetScale = Vector3.one;
    #endregion

    #region Material Property Settings
    [ShowIf("@animationPreset == AnimationPreset.MaterialProperty")]
    [FoldoutGroup("Material Property Settings")]
    [Tooltip("Material Property Name (z.B. '_Alpha', '_EmissionColor', '_MainTex_ST')")]
    public string materialPropertyName = "_Alpha";

    [ShowIf("@animationPreset == AnimationPreset.MaterialProperty")]
    [FoldoutGroup("Material Property Settings")]
    [Tooltip("Start-Wert für die Material Property")]
    public float startValue = 0f;

    [ShowIf("@animationPreset == AnimationPreset.MaterialProperty")]
    [FoldoutGroup("Material Property Settings")]
    [Tooltip("Ziel-Wert für die Material Property")]
    public float targetValue = 1f;

    [ShowIf("@animationPreset == AnimationPreset.MaterialProperty")]
    [FoldoutGroup("Material Property Settings")]
    [InfoBox("Automatisch Material von SpriteRenderer/Image detektieren oder manuell zuweisen")]
    public Material targetMaterial;
    #endregion

    #region Hover Scale (Legacy)
    [FoldoutGroup("Hover Effects"), Tooltip("Aktiviert Hover-Scale-Effekt")]
    public bool enableHoverScale = false;

    [FoldoutGroup("Hover Effects"), ShowIf("enableHoverScale"), MinValue(1f)]
    public float hoverScaleMultiplier = 1.1f;

    [FoldoutGroup("Hover Effects"), ShowIf("enableHoverScale"), MinValue(0f)]
    public float hoverScaleDuration = 0.2f;

    [FoldoutGroup("Hover Effects"), ShowIf("enableHoverScale"), EnumToggleButtons]
    public Ease hoverScaleEase = Ease.OutQuad;
    #endregion

    #region Pointer Stay Effects
    [FoldoutGroup("Pointer Stay Effects")]
    [EnumToggleButtons, Tooltip("Effekt während der Mauszeiger über dem Element verweilt")]
    public PointerStayEffect pointerStayEffect = PointerStayEffect.None;

    [ShowIf("@pointerStayEffect == PointerStayEffect.Pulse")]
    [FoldoutGroup("Pointer Stay Effects"), MinValue(1f)]
    public float stayPulseScale = 1.1f;

    [ShowIf("@pointerStayEffect == PointerStayEffect.Pulse")]
    [FoldoutGroup("Pointer Stay Effects"), MinValue(0.01f)]
    public float stayPulseDuration = 0.5f;

    [ShowIf("@pointerStayEffect == PointerStayEffect.Rotate")]
    [FoldoutGroup("Pointer Stay Effects")]
    public float stayRotationSpeed = 90f; // degrees per second

    [ShowIf("@pointerStayEffect == PointerStayEffect.Glow")]
    [FoldoutGroup("Pointer Stay Effects"), Range(0f, 2f)]
    public float stayGlowIntensity = 1.5f;

    [ShowIf("@pointerStayEffect == PointerStayEffect.Glow")]
    [FoldoutGroup("Pointer Stay Effects"), MinValue(0.01f)]
    public float stayGlowSpeed = 2f;
    #endregion

    #region Pointer Exit Effects
    [FoldoutGroup("Pointer Exit Effects")]
    [EnumToggleButtons, Tooltip("Effekt wenn der Mauszeiger das Element verlässt")]
    public PointerExitEffect pointerExitEffect = PointerExitEffect.None;

    [ShowIf("@pointerExitEffect == PointerExitEffect.FadeOut")]
    [FoldoutGroup("Pointer Exit Effects"), Range(0f,1f)]
    public float exitFadeTargetAlpha = 0f;

    [ShowIf("@pointerExitEffect != PointerExitEffect.None")]
    [FoldoutGroup("Pointer Exit Effects"), MinValue(0f)]
    public float exitEffectDuration = 0.2f;

    [ShowIf("@pointerExitEffect != PointerExitEffect.None")]
    [FoldoutGroup("Pointer Exit Effects"), EnumToggleButtons]
    public Ease exitEffectEase = Ease.OutQuad;

    [ShowIf("@pointerExitEffect == PointerExitEffect.ScaleDown")]
    [FoldoutGroup("Pointer Exit Effects"), Range(0f, 1f)]
    public float exitScaleTarget = 0.8f;

    [ShowIf("@pointerExitEffect == PointerExitEffect.RotateOut")]
    [FoldoutGroup("Pointer Exit Effects")]
    public float exitRotationAngle = 180f;
    #endregion

    #region Enable Fade Settings
    [FoldoutGroup("Enable Fade"), Tooltip("Fadet das Element beim Aktivieren automatisch ein (wenn kein Preset genutzt wird)")]
    public bool fadeInOnEnable = false;

    [FoldoutGroup("Enable Fade"), ShowIf("fadeInOnEnable"), MinValue(0f)]
    public float fadeInDuration = 0.3f;

    [FoldoutGroup("Enable Fade"), ShowIf("fadeInOnEnable"), EnumToggleButtons]
    public Ease fadeInEase = Ease.OutQuad;
    #endregion

    #region Player Detection Trigger
    [FoldoutGroup("Player Detection"), Tooltip("Triggert Animation wenn Player detected wird (nur einmal bis Reset)")]
    public bool enablePlayerDetectionTrigger = false;
    
    [FoldoutGroup("Player Detection"), ShowIf("enablePlayerDetectionTrigger")]
    [InfoBox("Target Layers die getriggert werden sollen (meist Player)")]
    public CollisionLayers triggerLayers = CollisionLayers.Player;
    
    [FoldoutGroup("Player Detection"), ShowIf("enablePlayerDetectionTrigger")]
    [Tooltip("Automatisch Animation abspielen wenn Player detected")]
    public bool playAnimationOnPlayerDetection = true;
    
    [FoldoutGroup("Player Detection"), ShowIf("enablePlayerDetectionTrigger"), DisplayAsString, ShowInInspector]
    private string PlayerDetectionStatus => hasBeenTriggered ? "TRIGGERED" : "Ready";
    
    [FoldoutGroup("Player Detection"), ShowIf("enablePlayerDetectionTrigger")]
    [Tooltip("Debug Logs für Player Detection (verursacht GC)")]
    public bool enablePlayerDetectionDebug = false;
    #endregion

    #region References
    [Title("Target References")]
    [InfoBox("Mindestens eine Referenz benötigt für Fade-Animationen", InfoMessageType.Warning, "@NeedsFadeTarget && !HasFadeTarget")]
    [FoldoutGroup("UI Components")]
    public CanvasGroup canvasGroup;
    [FoldoutGroup("UI Components")]
    public Image image;
    [FoldoutGroup("UI Components")]
    public RectTransform rectTransform;
    
    [FoldoutGroup("Sprite Components")]
    [InfoBox("Für Sprite-basierte GameObjects (non-UI)")]
    public SpriteRenderer spriteRenderer;
    #endregion

    // Runtime
    private Vector3 originalScale;
    private Vector3 originalRotation;
    private float originalAlpha;
    
    // Pool trigger state
    private bool hasBeenTriggered = false;
    
    // Material instance for unique property changes
    private Material materialInstance;
    
    // Animation state tracking
    private bool isAnimating = false;
    private System.Action currentCloseCallback;
    
    // Tween handles
    private Sequence animationSequence;
    private Tween hoverTween;
    private Sequence pulseSequence;
    private Sequence pointerStaySequence;
    private Sequence pointerExitSequence;
    private Sequence enableFadeSequence;
    private Sequence openCloseSequence;

    private bool NeedsFadeTarget => animationPreset != AnimationPreset.None && animationPreset != AnimationPreset.Pulse && animationPreset != AnimationPreset.ScaleToTarget;
    private bool HasFadeTarget => canvasGroup != null || image != null || spriteRenderer != null;

    private bool pointerInside = false;

    private bool isPointerStayActive = false;

    /// <summary>
    /// Returns true if any animation is currently playing
    /// </summary>
    public bool IsAnimating => isAnimating;

    private void Awake()
    {
        // Auto-cache references if not assigned
        if (rectTransform == null)
            rectTransform = GetComponent<RectTransform>();
        if (spriteRenderer == null)
            spriteRenderer = GetComponent<SpriteRenderer>();

        // Cache original values
        if (rectTransform != null)
        {
            originalScale = rectTransform.localScale;
            originalRotation = rectTransform.localEulerAngles;
        }
        else
        {
            originalScale = transform.localScale;
            originalRotation = transform.localEulerAngles;
        }

        originalAlpha = GetCurrentAlpha();

        // Validate
        if (NeedsFadeTarget && !HasFadeTarget)
        {
            Debug.LogWarning($"PrimeTweenEffect on '{gameObject.name}' needs CanvasGroup, Image, or SpriteRenderer for fade animations!");
        }
    }

    private void OnEnable()
    {
        // Optionale Fade-In-Animation (nur, wenn kein Preset verwendet wird)
        if (fadeInOnEnable && animationPreset == AnimationPreset.None && HasFadeTarget)
        {
            StartEnableFade();
        }

        if (playOnEnable && animationPreset != AnimationPreset.None)
        {
            Play();
        }
    }

    private void OnDisable()
    {
        Stop();
        ResetToOriginal();
    }
    
    private void OnDestroy()
    {
        // Clean up material instance to prevent memory leaks
        if (materialInstance != null)
        {
            if (Application.isPlaying)
            {
                Destroy(materialInstance);
            }
            else
            {
                DestroyImmediate(materialInstance);
            }
            materialInstance = null;
        }
    }

    #region Public Methods
    public void Play()
    {
        Stop();
        switch (animationPreset)
        {
            case AnimationPreset.FadeOnly:
                PlayFadeOnly();
                break;
            case AnimationPreset.ScaleIn:
                PlayScaleIn();
                break;
            case AnimationPreset.ScaleBounce:
                PlayScaleIn(true);
                break;
            case AnimationPreset.ScaleToTarget:
                PlayScaleToTarget();
                break;
            case AnimationPreset.MaterialProperty:
                PlayMaterialProperty();
                break;
            case AnimationPreset.Pulse:
                PlayPulse();
                break;
            case AnimationPreset.RotateIn:
                PlayRotateIn();
                break;
        }
    }

    public void Stop()
    {
        animationSequence.Stop();
        pulseSequence.Stop();
        pointerStaySequence.Stop();
        pointerExitSequence.Stop();
        enableFadeSequence.Stop();
        openCloseSequence.Stop();
        isAnimating = false;
        currentCloseCallback = null;
    }

    public void ResetToOriginal()
    {
        SetAlpha(originalAlpha);
        var target = rectTransform != null ? rectTransform.transform : transform;
        target.localScale = originalScale;
        target.localEulerAngles = originalRotation;
    }

    /// <summary>
    /// Plays the configured open animation preset
    /// </summary>
    public void PlayOpenAnimation()
    {
        if (isAnimating)
        {
            Stop(); // Stop any current animation
        }

        isAnimating = true;

        // Ensure object is active before playing open animation
        if (!gameObject.activeInHierarchy)
        {
            gameObject.SetActive(true);
        }

        // Play the configured animation preset
        Play();
        
        // Track when animation completes
        if (animationSequence.isAlive)
        {
            animationSequence.OnComplete(() => {
                isAnimating = false;
            });
        }
        else
        {
            isAnimating = false;
        }
    }

    /// <summary>
    /// Plays a close animation and optionally calls callback when complete
    /// </summary>
    public void PlayCloseAnimation(System.Action onCloseComplete = null)
    {
        if (isAnimating)
        {
            Stop(); // Stop any current animation
        }

        isAnimating = true;
        currentCloseCallback = onCloseComplete;

        var target = rectTransform != null ? rectTransform.transform : transform;

        // Create close animation based on current exit effect or fallback to fade
        PointerExitEffect closeEffect = pointerExitEffect != PointerExitEffect.None ? 
            pointerExitEffect : PointerExitEffect.FadeOut;

        openCloseSequence = CreateCloseAnimation(target, closeEffect);
        
        if (openCloseSequence.isAlive)
        {
            openCloseSequence.OnComplete(() => {
                isAnimating = false;
                currentCloseCallback?.Invoke();
                currentCloseCallback = null;
            });
        }
        else
        {
            // Fallback if animation failed
            isAnimating = false;
            currentCloseCallback?.Invoke();
            currentCloseCallback = null;
        }
    }

    /// <summary>
    /// Triggert Effekt von Pool/Systems-Component (nur einmal bis Reset)
    /// </summary>
    public void TriggerFromPool()
    {
        if (!enablePlayerDetectionTrigger || hasBeenTriggered) 
        {
            if (enablePlayerDetectionDebug && hasBeenTriggered)
            {
                Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Already triggered, ignoring");
            }
            return;
        }
        
        hasBeenTriggered = true;
        
        if (enablePlayerDetectionDebug)
        {
            Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Player detected! Triggering animation preset: {animationPreset}");
        }
        
        // Play animation if enabled
        if (playAnimationOnPlayerDetection && animationPreset != AnimationPreset.None)
        {
            Play();
            
            if (enablePlayerDetectionDebug)
            {
                Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Animation started");
            }
        }
        else if (enablePlayerDetectionDebug)
        {
            Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Animation not started (playOnDetection: {playAnimationOnPlayerDetection}, preset: {animationPreset})");
        }
    }

    /// <summary>
    /// Resettet Pool Trigger (für Object Pooling Systems)
    /// </summary>
    public void ResetPoolTrigger()
    {
        if (enablePlayerDetectionDebug)
        {
            Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Pool trigger reset - ready for new detection");
        }
        hasBeenTriggered = false;
        
        // Reset material instance for pooled objects
        if (materialInstance != null)
        {
            // Restore original material properties if needed
            // (The instance will be reused, so no need to destroy/recreate)
        }
    }
    
    private void OnTriggerEnter2D(Collider2D other)
    {
        if (!enablePlayerDetectionTrigger) return;
        
        if (enablePlayerDetectionDebug)
        {
            Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Trigger collision with {other.name}");
        }
        
        if (hasBeenTriggered)
        {
            if (enablePlayerDetectionDebug)
            {
                Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Already triggered, ignoring collision");
            }
            return;
        }
        
        // Check if colliding object is player
        if (other.CompareTag("Player"))
        {
            if (enablePlayerDetectionDebug)
            {
                Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Player detected!");
            }
            TriggerFromPool();
        }
        else if (enablePlayerDetectionDebug)
        {
            Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Not player, ignoring");
        }
    }
    #endregion

    #region Animation Implementations
    private void PlayFadeOnly()
    {
        SetAlpha(0f);
        animationSequence = StartSequence()
            .Chain(Tween.Custom(this, 0f, 1f, duration, (t, v) => t.SetAlpha(v), ease));
    }

    private void PlayScaleIn(bool bounce = false)
    {
        var target = rectTransform != null ? rectTransform.transform : transform;
        target.localScale = Vector3.zero;
        SetAlpha(0f);
        animationSequence = StartSequence()
            .Group(Tween.Custom(this, 0f, 1f, duration, (t, v) => t.SetAlpha(v), ease))
            .Group(Tween.Scale(target, originalScale, duration, bounce ? Ease.OutBack : ease));
    }

    private void PlayPulse()
    {
        var target = rectTransform != null ? rectTransform.transform : transform;
        
        pulseSequence = Sequence.Create(cycles: pulseLoops, useUnscaledTime: true)
            .Chain(Tween.Scale(target, originalScale * pulseScale, duration * 0.5f, Ease.InOutSine))
            .Chain(Tween.Scale(target, originalScale, duration * 0.5f, Ease.InOutSine));
    }

    private void PlayScaleToTarget()
    {
        var target = rectTransform != null ? rectTransform.transform : transform;
        target.localScale = startScale;
        animationSequence = StartSequence()
            .Chain(Tween.Scale(target, targetScale, duration, ease));
    }

    private void PlayMaterialProperty()
    {
        Material material = GetTargetMaterial();
        if (material == null)
        {
            Debug.LogWarning($"PrimeTweenEffect: No material found for property animation on '{gameObject.name}'!");
            return;
        }
        
        if (string.IsNullOrEmpty(materialPropertyName))
        {
            Debug.LogWarning($"PrimeTweenEffect: Material property name is empty on '{gameObject.name}'!");
            return;
        }
        
        // Set start value
        material.SetFloat(materialPropertyName, startValue);
        
        // Animate to target value
        animationSequence = StartSequence()
            .Chain(Tween.Custom(startValue, targetValue, duration, 
                value => material.SetFloat(materialPropertyName, value), ease));
    }

    private void PlayRotateIn()
    {
        var target = rectTransform != null ? rectTransform.transform : transform;
        target.localEulerAngles = originalRotation + new Vector3(0, 0, -180);
        target.localScale = Vector3.zero;
        SetAlpha(0f);
        animationSequence = StartSequence()
            .Group(Tween.Custom(this, 0f, 1f, duration, (t, v) => t.SetAlpha(v), ease))
            .Group(Tween.Scale(target, originalScale, duration, ease))
            .Group(Tween.LocalRotation(target, Quaternion.Euler(originalRotation), duration, ease));
    }
    #endregion

    #region Hover Handlers
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (!enableHoverScale && pointerStayEffect == PointerStayEffect.None && pointerExitEffect == PointerExitEffect.None) return;

        pointerInside = true;
        var target = rectTransform != null ? rectTransform.transform : transform;

        // Beende ggf. Exit-Effekt und stelle Original-Werte wieder her
        if (pointerExitEffect != PointerExitEffect.None)
        {
            pointerExitSequence.Stop();
            ResetAfterExitEffect();
        }

        if (enableHoverScale)
        {
            hoverTween = Tween.Scale(target, originalScale * hoverScaleMultiplier,
                hoverScaleDuration, hoverScaleEase, useUnscaledTime: true);
        }

        if (pointerStayEffect != PointerStayEffect.None)
        {
            StartPointerStayEffect(target);
        }
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (!enableHoverScale && pointerStayEffect == PointerStayEffect.None && pointerExitEffect == PointerExitEffect.None) return;

        pointerInside = false;
        var target = rectTransform != null ? rectTransform.transform : transform;

        if (enableHoverScale)
        {
            hoverTween = Tween.Scale(target, originalScale,
                hoverScaleDuration, hoverScaleEase, useUnscaledTime: true);
        }

        if (pointerStayEffect != PointerStayEffect.None)
        {
            StopPointerStayEffect(target);
        }

        if (pointerExitEffect != PointerExitEffect.None)
        {
            StartPointerExitEffect(target);
        }
    }
    #endregion

    #region Helper Methods
    private float GetCurrentAlpha()
    {
        if (canvasGroup != null) return canvasGroup.alpha;
        if (image != null) return image.color.a;
        if (spriteRenderer != null) return spriteRenderer.color.a;
        return 1f;
    }

    private void SetAlpha(float alpha)
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = alpha;
        }
        else if (image != null)
        {
            var color = image.color;
            image.color = new Color(color.r, color.g, color.b, alpha);
        }
        else if (spriteRenderer != null)
        {
            var color = spriteRenderer.color;
            spriteRenderer.color = new Color(color.r, color.g, color.b, alpha);
        }
    }
    
    private Material GetTargetMaterial()
    {
        // Create unique material instance if not exists
        if (materialInstance == null)
        {
            Material sourceMaterial = null;
            
            // Use manually assigned material first
            if (targetMaterial != null)
            {
                sourceMaterial = targetMaterial;
            }
            // Auto-detect from components
            else if (spriteRenderer != null)
            {
                sourceMaterial = spriteRenderer.sharedMaterial;
            }
            else if (image != null)
            {
                sourceMaterial = image.material;
            }
            
            if (sourceMaterial != null)
            {
                // Create unique instance
                materialInstance = new Material(sourceMaterial);
                
                // Assign instance to component
                if (spriteRenderer != null)
                {
                    spriteRenderer.material = materialInstance;
                }
                else if (image != null)
                {
                    image.material = materialInstance;
                }
                
                if (enablePlayerDetectionDebug)
                {
                    Debug.Log($"[PrimeTweenEffect] {gameObject.name}: Created unique material instance from {sourceMaterial.name}");
                }
            }
        }
        
        return materialInstance;
    }

    // Creates a sequence with optional start delay (uses unscaled time by default)
    private Sequence StartSequence()
    {
        var seq = Sequence.Create(useUnscaledTime: true);
        if (delay > 0f)
            seq.Chain(Tween.Delay(delay, useUnscaledTime: true));
        return seq;
    }

    private void StartPointerStayEffect(Transform target)
    {
        pointerStaySequence.Stop();
        isPointerStayActive = true;
        switch (pointerStayEffect)
        {
            case PointerStayEffect.Pulse:
                pointerStaySequence = Sequence.Create(cycles: -1, useUnscaledTime: true)
                    .Chain(Tween.Scale(target, originalScale * stayPulseScale, stayPulseDuration * 0.5f, Ease.InOutSine))
                    .Chain(Tween.Scale(target, originalScale, stayPulseDuration * 0.5f, Ease.InOutSine));
                break;
            case PointerStayEffect.Rotate:
                pointerStaySequence = Sequence.Create(cycles: -1, useUnscaledTime: true)
                    .Chain(Tween.LocalRotation(target, target.localRotation * Quaternion.Euler(0, 0, 360), 360f / stayRotationSpeed, Ease.Linear));
                break;
            case PointerStayEffect.Glow:
                pointerStaySequence = Sequence.Create(cycles: -1, useUnscaledTime: true)
                    .Group(Tween.Scale(target, originalScale * stayGlowIntensity, stayGlowSpeed * 0.5f, Ease.InOutSine))
                    .Group(Tween.Custom(this, originalAlpha, originalAlpha * stayGlowIntensity, stayGlowSpeed * 0.5f, (t, v) => t.SetAlpha(v), Ease.InOutSine))
                    .Chain(Tween.Scale(target, originalScale, stayGlowSpeed * 0.5f, Ease.InOutSine))
                    .Group(Tween.Custom(this, originalAlpha * stayGlowIntensity, originalAlpha, stayGlowSpeed * 0.5f, (t, v) => t.SetAlpha(v), Ease.InOutSine));
                break;
        }
    }

    private void StopPointerStayEffect(Transform target)
    {
        pointerStaySequence.Stop();
        isPointerStayActive = false;
        target.localScale = originalScale;
        target.localRotation = Quaternion.Euler(originalRotation);
        SetAlpha(originalAlpha);
    }

    private void StartPointerExitEffect(Transform target)
    {
        pointerExitSequence.Stop();
        switch (pointerExitEffect)
        {
            case PointerExitEffect.FadeOut:
                float startAlpha = GetCurrentAlpha();
                pointerExitSequence = Sequence.Create(useUnscaledTime: true)
                    .Chain(Tween.Custom(this, startAlpha, exitFadeTargetAlpha, exitEffectDuration, (t, v) => t.SetAlpha(v), exitEffectEase));
                break;
            case PointerExitEffect.ScaleDown:
                pointerExitSequence = Sequence.Create(useUnscaledTime: true)
                    .Chain(Tween.Scale(target, originalScale * exitScaleTarget, exitEffectDuration, exitEffectEase));
                break;
            case PointerExitEffect.RotateOut:
                var targetRotation = Quaternion.Euler(originalRotation + new Vector3(0, 0, exitRotationAngle));
                pointerExitSequence = Sequence.Create(useUnscaledTime: true)
                    .Chain(Tween.LocalRotation(target, targetRotation, exitEffectDuration, exitEffectEase));
                break;
        }
    }

    private void ResetAfterExitEffect()
    {
        SetAlpha(originalAlpha);
        var target = rectTransform != null ? rectTransform.transform : transform;
        target.localScale = originalScale;
        target.localRotation = Quaternion.Euler(originalRotation);
    }

    private void StartEnableFade()
    {
        enableFadeSequence.Stop();
        SetAlpha(0f);
        enableFadeSequence = Sequence.Create(useUnscaledTime: true)
            .Chain(Tween.Custom(this, 0f, originalAlpha, fadeInDuration, (t, v) => t.SetAlpha(v), fadeInEase));
    }

    /// <summary>
    /// Creates a close animation sequence based on the specified effect type
    /// </summary>
    private Sequence CreateCloseAnimation(Transform target, PointerExitEffect closeEffect)
    {
        float duration = exitEffectDuration > 0 ? exitEffectDuration : 0.3f;
        Ease easing = exitEffectEase;

        switch (closeEffect)
        {
            case PointerExitEffect.FadeOut:
                float startAlpha = GetCurrentAlpha();
                float targetAlpha = exitFadeTargetAlpha;
                return Sequence.Create(useUnscaledTime: true)
                    .Chain(Tween.Custom(this, startAlpha, targetAlpha, duration, (t, v) => t.SetAlpha(v), easing));

            case PointerExitEffect.ScaleDown:
                Vector3 targetScale = originalScale * exitScaleTarget;
                return Sequence.Create(useUnscaledTime: true)
                    .Group(Tween.Scale(target, targetScale, duration, easing))
                    .Group(Tween.Custom(this, GetCurrentAlpha(), 0f, duration, (t, v) => t.SetAlpha(v), easing));

            case PointerExitEffect.RotateOut:
                var targetRotation = Quaternion.Euler(originalRotation + new Vector3(0, 0, exitRotationAngle));
                return Sequence.Create(useUnscaledTime: true)
                    .Group(Tween.LocalRotation(target, targetRotation, duration, easing))
                    .Group(Tween.Custom(this, GetCurrentAlpha(), 0f, duration, (t, v) => t.SetAlpha(v), easing));

            default:
                // Fallback to simple fade out
                return Sequence.Create(useUnscaledTime: true)
                    .Chain(Tween.Custom(this, GetCurrentAlpha(), 0f, duration, (t, v) => t.SetAlpha(v), easing));
        }
    }
    #endregion
} 