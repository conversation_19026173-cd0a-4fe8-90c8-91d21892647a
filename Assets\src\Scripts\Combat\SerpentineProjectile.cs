using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(Collider2D))]
public class SerpentineProjectile : MonoBehaviour, ISpawnable, ICollisionHandler
{
    [Title("Projectile Settings")]
    [SerializeField] private float defaultSpeed = 10f;
    [SerializeField] private float defaultLifetime = 2f;
    [SerializeField] private float baseDamage = 10f;
    
    [Title("Serpentine Movement")]
    [SerializeField] private float waveAmplitude = 0.08f;
    [Tooltip("Higher frequency = tighter curves")]
    [SerializeField] private float waveFrequency = 8f;
    [Tooltip("Delay before serpentine movement starts")]
    [SerializeField] private float serpentineDelay = 0.2f;
    
    
    [Title("Debug Settings")]
    [SerializeField] private bool enableForkDebugLogging = true;
    [Tooltip("Enable debug logging for fork projectile status effect issues")]

    [Title("Particle Effects")]
    [SerializeField] private bool useImpactParticles = true;
    [ShowIf("useImpactParticles")]
    [SerializeField] private ParticleType impactParticleType = ParticleType.SparkImpact;
    [ShowIf("useImpactParticles")]
    [SerializeField] private int impactParticleCount = 10;
    
    [SerializeField] private bool useTrailParticles = false;
    [ShowIf("useTrailParticles")]
    [SerializeField] private ParticleType trailParticleType = ParticleType.FireTrail;
    [ShowIf("useTrailParticles")]
    [SerializeField] private float trailInterval = 0.1f;
    
    [SerializeField] private bool useDespawnParticles = true;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private ParticleType despawnParticleType = ParticleType.SmokeImpact;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private int despawnParticleCount = 5;
    
    [InfoBox("Optional: Assign a child transform for precise particle spawn position")]
    [SerializeField] private Transform particleSpawnPoint;
    
    // Runtime values
    public float speed { get; set; }
    public float lifetime { get; set; }
    public float damage { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Physical;
    public float ailmentChance { get; set; } = 0f;

    // Gem data for status effect configuration
    public SkillGemData skillGemData { get; set; }
    public System.Collections.Generic.List<GemInstance> supportGems { get; set; }
    
    // Damage breakdown for type-specific damage
    public DamageBreakdown? damageBreakdown { get; set; }
    
    // Support gem effects
    private bool isPiercing;
    private int pierceCount;
    private int currentPierces;
    
    private bool isChaining;
    private int chainCount;
    private int currentChains;
    private GameObject lastTarget;
    
    private bool isFork;
    private int forkCount;
    private float forkAngle;
    private bool hasForked;
    
    private bool hasAreaDamage;
    private float areaRadius;
     
    private Vector2 _baseDirection;
    private Vector2 _perpendicularDirection;
    private float _timeAlive;
    private Vector3 _startPosition;
    private Collider2D _collider2D;
    private bool _isActive;
    private LayerMask _currentLayerMask;

    // Performance optimization for Update() timing
    private int _framesSinceLastCheck;
    private const int COLLISION_CHECK_FRAME_INTERVAL = 1; // Check every frame by default
    
    // Serpentine-specific runtime values
    public float amplitude { get; set; }
    public float frequency { get; set; }
    public float delay { get; set; }
    
    // Random amplitude multiplier for organic variation
    private float _amplitudeMultiplier = 1f;
    
    // Cache for nearby colliders to eliminate allocations in area damage detection
    private readonly System.Collections.Generic.List<Collider2D> _nearbyCollidersCache = new System.Collections.Generic.List<Collider2D>(32);
    private readonly Collider2D[] _overlapResults = new Collider2D[32];
    
    private void Awake()
    {
        _collider2D = GetComponent<Collider2D>();
        // Ensure the collider is set as trigger for proper collision detection
        _collider2D.isTrigger = true;
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageValue = 1f, int layer = 10)
    {
        Initialize(position, direction, damageValue, layer, defaultSpeed, defaultLifetime);
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageValue, int layer, float projectileSpeed, float projectileLifetime)
    {
        // Validate input parameters
        if (direction == Vector2.zero)
        {
            Debug.LogError($"[SerpentineProjectile] {name} Initialize() called with zero direction vector!");
            direction = Vector2.right; // Default fallback
        }
        if (projectileSpeed <= 0f)
        {
            Debug.LogError($"[SerpentineProjectile] {name} Initialize() called with invalid speed: {projectileSpeed}");
            projectileSpeed = defaultSpeed; // Use default speed
        }

        transform.position = position;
        _startPosition = position;
        _baseDirection = direction.normalized;
        _perpendicularDirection = new Vector2(-_baseDirection.y, _baseDirection.x);
        _timeAlive = 0f;

        this.damage = damageValue;
        this.speed = projectileSpeed;
        this.lifetime = projectileLifetime;
        
        // Set default serpentine values if not configured
        if (amplitude == 0f) amplitude = waveAmplitude;
        if (frequency == 0f) frequency = waveFrequency;
        if (delay == 0f) delay = serpentineDelay;
        
        // Random amplitude multiplier for organic variation (-1 to 1)
        _amplitudeMultiplier = Random.Range(-1f, 1f);
        
        _isActive = true;

        // DEBUG: Log layer assignment before and after
        Debug.Log($"[SerpentineProjectile] {name} Layer assignment - Before: {gameObject.layer}, Setting to: {layer}");

        // Set Unity layer based on CollisionLayers enum
        gameObject.layer = layer;

        // DEBUG: Verify layer assignment
        Debug.Log($"[SerpentineProjectile] {name} Layer assignment - After: {gameObject.layer}, Success: {gameObject.layer == layer}");

        // Set collision layer mask - enemies can hit player, player projectiles can hit enemies
        if (layer == 10) // PlayerProjectile
        {
            _currentLayerMask = 1 << 9; // Enemy layer
        }
        else if (layer == 11) // EnemyProjectile
        {
            _currentLayerMask = 1 << 8; // Player layer
        }
        else
        {
            _currentLayerMask = ~0; // All layers as fallback
        }

        // DEBUG: Log collision layer mask
        Debug.Log($"[SerpentineProjectile] {name} Collision LayerMask set to: {_currentLayerMask} (binary: {System.Convert.ToString(_currentLayerMask, 2)})");

        // DEBUG: Verify layer collision matrix
        bool canHitSelf = !Physics2D.GetIgnoreLayerCollision(layer, layer);
        Debug.Log($"[SerpentineProjectile] {name} Can hit self (layer {layer}): {canHitSelf}");
        
        // Start trail particles if enabled
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StartContinuousEffect(trailParticleType, transform, trailInterval);
        }

        // DEBUG: Log initialization values
        Debug.Log($"[SerpentineProjectile] {name} Initialized - Position: {position}, BaseDirection: {_baseDirection}, Speed: {speed}, Damage: {damage}, Active: {_isActive}, Layer: {layer}");
    }
    
    private void Update()
    {
        if (!_isActive)
        {
            // DEBUG: Log why projectile is not active
            Debug.Log($"[SerpentineProjectile] {name} Update skipped - _isActive: {_isActive}");
            return;
        }

        _timeAlive += Time.deltaTime;
        
        // Check if exceeded lifetime
        if (_timeAlive >= lifetime)
        {
            SpawnDespawnParticles();
            Deactivate();
            return;
        }
        
        // Calculate forward movement using frame-rate independent timing
        float forwardDistance = speed * Time.deltaTime;
        Vector2 forwardMovement = _baseDirection * forwardDistance;
        
        // Calculate serpentine offset if delay has passed and frequency > 0
        Vector2 lateralMovement = Vector2.zero;
        if (_timeAlive >= delay && frequency > 0f)
        {
            float wavePhase = (_timeAlive - delay) * frequency;
            float lateralOffset = Mathf.Sin(wavePhase) * amplitude * _amplitudeMultiplier;
            lateralMovement = _perpendicularDirection * lateralOffset;
        }
        
        // Total movement for this frame
        Vector2 totalMovement = forwardMovement + lateralMovement;
        Vector2 currentPos = transform.position;
        Vector2 nextPos = currentPos + totalMovement;
        float moveDistance = totalMovement.magnitude;

        // DEBUG: Log movement calculations
        Debug.Log($"[SerpentineProjectile] {name} Movement - CurrentPos: {currentPos}, BaseDirection: {_baseDirection}, Speed: {speed}, ForwardMovement: {forwardMovement}, LateralMovement: {lateralMovement}, TotalMovement: {totalMovement}, NextPos: {nextPos}");

        // Performance-optimized collision detection
        // Use frame-based limiting for high frame rates to maintain performance
        bool shouldCheckCollision = ShouldPerformCollisionCheck();

        if (shouldCheckCollision && moveDistance > 0f)
        {
            float collisionRadius = GetCollisionRadius();
            LayerMask collisionMask = GetCollisionLayerMask();
            Vector2 moveDirection = totalMovement.normalized;

            // DEBUG: Log collision detection parameters
            Debug.Log($"[SerpentineProjectile] {name} Collision Check - Radius: {collisionRadius}, LayerMask: {collisionMask}, MoveDirection: {moveDirection}, MoveDistance: {moveDistance}, IgnoringSelf: {_collider2D != null}");

            // Use the overload that ignores self-collision
            if (RaycastCollisionHelper.CircleCastCheck(currentPos, collisionRadius, moveDirection, moveDistance, collisionMask, _collider2D, out RaycastHit2D hit))
            {
                // DEBUG: Log collision detection
                Debug.Log($"[SerpentineProjectile] {name} COLLISION DETECTED - Hit: {hit.collider.name}, Layer: {hit.collider.gameObject.layer}, Point: {hit.point}, Distance: {hit.distance}");

                // Handle collision at hit point
                HandleCollisionEvent(hit.collider, hit.point);
                return; // Don't move if we hit something
            }
            else
            {
                // DEBUG: Log when no collision is detected
                Debug.Log($"[SerpentineProjectile] {name} No collision detected - proceeding with movement");
            }
        }
        else
        {
            // DEBUG: Log when collision check is skipped
            Debug.Log($"[SerpentineProjectile] {name} Collision check skipped - ShouldCheck: {shouldCheckCollision}, MoveDistance: {moveDistance}");
        }

        // Apply movement
        Vector3 oldPosition = transform.position;
        transform.position = nextPos;

        // DEBUG: Log position update
        Debug.Log($"[SerpentineProjectile] {name} Position Updated - From: {oldPosition} To: {transform.position}");

        // Update rotation to face movement direction only if we have significant movement
        if (totalMovement.magnitude > 0.001f)
        {
            float angle = Mathf.Atan2(totalMovement.y, totalMovement.x) * Mathf.Rad2Deg;
            transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        }
        else if (frequency == 0f)
        {
            // If no serpentine movement, just face the base direction
            float angle = Mathf.Atan2(_baseDirection.y, _baseDirection.x) * Mathf.Rad2Deg;
            transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        }
    }
    
    #region ICollisionHandler Implementation
    
    public void HandleCollisionEvent(Collider2D collider, Vector2 hitPoint)
    {
        // Skip if we're chaining and this is our last target
        if (isChaining && collider.gameObject == lastTarget)
            return;
        
        // Process collision with the target
        if (ProcessCollision(collider.gameObject, hitPoint))
        {
            // Collision was processed successfully - handle special effects
            HandleSuccessfulCollision(collider, hitPoint);
        }
        else
        {
            // Check if it's a wall/environment to despawn
            LayerMask wallLayers = (1 << 12) | (1 << 15); // Wall + Environment layers
            if (((1 << collider.gameObject.layer) & wallLayers) != 0)
            {
                // Spawn impact particles for wall hits too
                SpawnImpactParticles(hitPoint);
                Deactivate();
            }
        }
    }
    
    public bool ProcessCollision(GameObject target, Vector2 position)
    {
        // Process collision - same logic as original ApplyDamage
        return ApplyDamage(target);
    }
    
    public float GetCollisionRadius()
    {
        if (_collider2D != null)
        {
            // Get radius from collider bounds
            Bounds bounds = _collider2D.bounds;
            return Mathf.Max(bounds.extents.x, bounds.extents.y);
        }
        return 0.5f; // Default radius
    }
    
    public LayerMask GetCollisionLayerMask()
    {
        return _currentLayerMask;
    }
    
    public bool IsCollisionActive()
    {
        return _isActive;
    }
    
    #endregion
    
    
    
    
    /// <summary>
    /// Handle successful collision with special effects (area damage, forking, chaining, piercing)
    /// </summary>
    private void HandleSuccessfulCollision(Collider2D collider, Vector2 hitPoint)
    {
        // Spawn impact particles
        SpawnImpactParticles(hitPoint);
        
        // Handle area damage
        if (hasAreaDamage)
        {
            ApplyAreaDamage(hitPoint);
        }
        
        // Handle forking (fork takes priority over chain)
        if (isFork && !hasForked)
        {
            ForkProjectiles(collider);
            hasForked = true;
            // Deactivate the original projectile after forking
            Deactivate();
            return;
        }
        
        // Handle chaining
        if (isChaining && currentChains < chainCount)
        {
            ChainToNextTarget(collider.gameObject);
            currentChains++;
            return;
        }
        
        // Handle piercing
        if (isPiercing && currentPierces < pierceCount)
        {
            currentPierces++;
            return; // Continue through target
        }
        
        // Deactivate only if we successfully hit a damageable object.
        Deactivate();
    }
    
    private bool ApplyDamage(GameObject target)
    {   
        // Safety check: Don't apply damage if we have 0 damage (indicates reset state)
        if (damage <= 0f)
        {
            return false;
        }

        // Calculate final damage with crit
        float finalDamage = damage;
        bool isCrit = Random.Range(0f, 100f) < critChance;
        if (isCrit)
        {
            finalDamage *= critMultiplier;
        }

        // Create damage info with gem data for status effect configuration
        DamageInfo damageInfo;
        if (damageBreakdown.HasValue)
        {
            // Use damage breakdown if available (includes conversion info)
            damageInfo = DamageInfo.FromBreakdown(
                damageBreakdown.Value,
                isCrit,
                critMultiplier,
                "SerpentineProjectile",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        else if (skillGemData != null)
        {
            // Player skill without breakdown - this should not happen!
            Debug.LogError($"[SerpentineProjectile] Player skill '{skillGemData.gemName}' missing damage breakdown! Support gem effects like Brutality Support will be bypassed.");
            return false;
        }
        else
        {
            // Agent/Enemy skill fallback - use single damage type
            damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                damageType,
                isCrit,
                critMultiplier,
                "SerpentineProjectile",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        
        // First, handle player damage via PlayerManager to avoid unnecessary pool lookups
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
            PlayerManager.DealDamageToPlayer(damageInfo);
            return true;
        }

        // Check for CombatantHealth first (enemies use this) - use PoolManager for GC-free lookups
        if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
        {
            combatantHealth.TakeDamage(damageInfo);
            return true;
        }
        // Fall back to HealthComponent for other targets - also use PoolManager
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
            healthComponent.TakeDamage(damageInfo);
            return true;
        }

        return false;
    }
    
    private void ApplyAreaDamage(Vector3 center)
    {
        // Find all enemies in radius using RaycastCollisionHelper
        LayerMask enemyLayer = 1 << 9; // Enemy layer
        int hitCount = RaycastCollisionHelper.OverlapCircleNonAlloc(center, areaRadius, _overlapResults, enemyLayer);
        
        _nearbyCollidersCache.Clear();
        for (int i = 0; i < hitCount; i++)
        {
            _nearbyCollidersCache.Add(_overlapResults[i]);
        }
            
        foreach (var collider in _nearbyCollidersCache)
        {
            var target = collider.gameObject;
            if (target == gameObject) continue; // Skip self
            
            // Apply reduced damage for area effect (70% of base)
            float areaDamage = damage * 0.7f;
            bool isCrit = Random.Range(0f, 100f) < critChance;
            if (isCrit)
            {
                areaDamage *= critMultiplier;
            }
            
            // Create damage info for area damage with gem data
            DamageInfo areaDamageInfo;
            if (damageBreakdown.HasValue)
            {
                // Scale damage breakdown for area damage
                var scaledBreakdown = damageBreakdown.Value;
                float areaMultiplier = 0.7f;
                scaledBreakdown.physicalDamage *= areaMultiplier;
                scaledBreakdown.fireDamage *= areaMultiplier;
                scaledBreakdown.iceDamage *= areaMultiplier;
                scaledBreakdown.lightningDamage *= areaMultiplier;

                areaDamageInfo = DamageInfo.FromBreakdown(
                    scaledBreakdown,
                    isCrit,
                    critMultiplier,
                    "SerpentineProjectile_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            else if (skillGemData != null)
            {
                // Player skill without breakdown - this should not happen!
                Debug.LogError($"[SerpentineProjectile] Player skill '{skillGemData.gemName}' missing damage breakdown for area damage! Support gem effects like Brutality Support will be bypassed.");
                return;
            }
            else
            {
                // Agent/Enemy skill fallback - use single damage type for area damage
                areaDamageInfo = DamageInfo.FromSingleType(
                    Mathf.RoundToInt(areaDamage),
                    damageType,
                    isCrit,
                    critMultiplier,
                    "SerpentineProjectile_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            
            // Handle player damage via PlayerManager
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(areaDamageInfo);
            }
            // Handle enemy damage via PoolManager - check CombatantHealth first for GC-free lookups
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
            {
                combatantHealth.TakeDamage(areaDamageInfo);
            }
            // Fall back to HealthComponent via PoolManager
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(areaDamageInfo);
            }
        }
    }
    
    private void ChainToNextTarget(GameObject currentTarget)
    {
        lastTarget = currentTarget;
        
        // Find nearest enemy within chain range using RaycastCollisionHelper
        float searchRadius = 5f;
        LayerMask enemyLayer = 1 << 9; // Enemy layer
        int hitCount = RaycastCollisionHelper.OverlapCircleNonAlloc(transform.position, searchRadius, _overlapResults, enemyLayer);
        
        _nearbyCollidersCache.Clear();
        for (int i = 0; i < hitCount; i++)
        {
            _nearbyCollidersCache.Add(_overlapResults[i]);
        }
        
        GameObject nearestTarget = null;
        float nearestDistance = float.MaxValue;
        
        foreach (var collider in _nearbyCollidersCache)
        {
            var target = collider.gameObject;
            if (target == currentTarget) continue; // Skip current target
            if (target == gameObject) continue; // Skip self
            
            // Check if target has health (is damageable) - use PoolManager for GC-free lookups
            bool hasHealth = false;
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                hasHealth = true;
            }
            else if (PoolManager.Instance != null && 
                    (PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var _) ||
                     PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var _)))
            {
                hasHealth = true;
            }
            
            if (hasHealth)
            {
                float distance = Vector2.Distance(transform.position, target.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = target;
                }
            }
        }
        
        if (nearestTarget != null)
        {
            // Redirect projectile to new target
            Vector2 newDirection = (nearestTarget.transform.position - transform.position).normalized;
            _baseDirection = newDirection;
            _perpendicularDirection = new Vector2(-_baseDirection.y, _baseDirection.x);
            
            // Reset lifetime and position tracking for chain
            _timeAlive = 0f;
            _startPosition = transform.position;
            
            // Reduce damage for each chain (80% of previous)
            damage *= 0.8f;
            
            // Scale damage breakdown to match reduced damage
            if (damageBreakdown.HasValue)
            {
                var scaledBreakdown = damageBreakdown.Value;
                scaledBreakdown.ScaleAllDamage(0.8f);
                damageBreakdown = scaledBreakdown;
            }
        }
        else
        {
            // No valid targets, deactivate
            Deactivate();
        }
    }
    
    private void ForkProjectiles(Collider2D collider)
    {
        if (PoolManager.Instance == null || collider == null) return;
        
        Vector2 impactPoint = collider.ClosestPoint(transform.position);
        Vector2 collisionNormal = (transform.position - (Vector3)impactPoint).normalized;
        
        // Calculate offset based on the hit object's size
        float spawnOffset = 0.2f; // Base offset
        
        if (collider is CircleCollider2D circleCollider)
        {
            // For circles, use radius
            spawnOffset += circleCollider.radius;
        }
        else if (collider is BoxCollider2D boxCollider)
        {
            // For boxes, use the larger dimension
            spawnOffset += Mathf.Max(boxCollider.size.x, boxCollider.size.y) * 0.5f;
        }
        else if (collider is CapsuleCollider2D capsuleCollider)
        {
            // For capsules, use the larger dimension
            spawnOffset += Mathf.Max(capsuleCollider.size.x, capsuleCollider.size.y) * 0.5f;
        }
        
        // Add a small safety margin
        spawnOffset += 0.3f;
        
        // Spawn point: offset from impact point along the collision normal
        Vector2 spawnOrigin = impactPoint + (collisionNormal * spawnOffset);
        
        // Calculate the base angle spread
        float anglePerFork = forkCount > 1 ? forkAngle / (forkCount - 1) : 0f;
        float startAngle = -forkAngle / 2f;
        
        // Get the current projectile's direction angle
        float baseAngle = Mathf.Atan2(_baseDirection.y, _baseDirection.x) * Mathf.Rad2Deg;
        
        // Spawn forked projectiles
        for (int i = 0; i < forkCount; i++)
        {
            float currentAngle = baseAngle + startAngle + (anglePerFork * i);
            float radians = currentAngle * Mathf.Deg2Rad;
            Vector2 forkDirection = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));
            
            // Retrieve the original prefab for the current projectile
            GameObject projectilePrefab = PoolManager.Instance.GetOriginalPrefab(gameObject) ?? gameObject;
            
            // Spawn a new projectile from the original prefab
            GameObject forkedProjectile = PoolManager.Instance.Spawn(projectilePrefab, spawnOrigin, Quaternion.Euler(0, 0, currentAngle));
            if (forkedProjectile != null && PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<SerpentineProjectile>(forkedProjectile, out var projectileComponent))
            {
                // Initialize the forked projectile first
                float forkedDamage = damage * 0.7f;
                projectileComponent.Initialize(spawnOrigin, forkDirection, forkedDamage, gameObject.layer, speed, lifetime);

                // Set serpentine properties
                projectileComponent.amplitude = amplitude;
                projectileComponent.frequency = frequency;
                projectileComponent.delay = delay;

                // CRITICAL: Set status effect properties AFTER Initialize to ensure they persist
                projectileComponent.damageType = damageType;
                projectileComponent.ailmentChance = ailmentChance;
                projectileComponent.skillGemData = skillGemData;
                projectileComponent.supportGems = supportGems;
                projectileComponent.critChance = critChance;
                projectileComponent.critMultiplier = critMultiplier;
                
                // CRITICAL: Transfer damage breakdown to preserve support gem effects (e.g., Brutality Support)
                if (damageBreakdown.HasValue)
                {
                    var scaledBreakdown = damageBreakdown.Value;
                    // Scale the breakdown to match the forked damage (70% of original)
                    scaledBreakdown.ScaleAllDamage(0.7f);
                    projectileComponent.damageBreakdown = scaledBreakdown;
                }

                // Forked projectiles can pierce but not fork again
                if (isPiercing)
                {
                    projectileComponent.SetPiercing(true, pierceCount);
                }

                // Disable forking on the forked projectiles to prevent infinite forks
                projectileComponent.SetFork(false);
            }
        }
    }
    
    private void SpawnImpactParticles(Vector2 impactPoint)
    {
        if (!useImpactParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use impact point
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : (Vector3)impactPoint;
        
        ParticleEffectManager.Instance.SpawnParticle(impactParticleType, spawnPosition, impactParticleCount);
    }
    
    private void SpawnDespawnParticles()
    {
        if (!useDespawnParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use current position
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : transform.position;
        
        ParticleEffectManager.Instance.SpawnParticle(despawnParticleType, spawnPosition, despawnParticleCount);
    }
    
    private void Deactivate()
    {
        _isActive = false;
        
        // Stop trail particles if active
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        if (PoolManager.Instance != null)
        {
            PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
            gameObject.SetActive(false);
        }
    }
    
    // ISpawnable implementation
    public void OnSpawn()
    {
        // Don't set _isActive here - let Initialize() handle activation
        // This prevents projectiles from being active before they're properly initialized
        // DEBUG: Log OnSpawn call
        Debug.Log($"[SerpentineProjectile] {name} OnSpawn() called - waiting for Initialize() to activate");
    }
    
    public void OnDespawn()
    {
        _isActive = false;
        // DEBUG: Log OnDespawn call
        Debug.Log($"[SerpentineProjectile] {name} OnDespawn() called - _isActive set to: {_isActive}");
        damage = 0f; // Reset damage (will be set by Initialize)
        speed = defaultSpeed; // Reset speed
        lifetime = defaultLifetime; // Reset lifetime
        damageType = DamageType.Physical; // Reset damage type
        ailmentChance = 0f; // Reset ailment chance
        damageBreakdown = null; // Reset damage breakdown

        // Clear gem data references
        skillGemData = null;
        supportGems = null;
        
        // Reset support gem effects
        isPiercing = false;
        pierceCount = 0;
        currentPierces = 0;
        
        isChaining = false;
        chainCount = 0;
        currentChains = 0;
        lastTarget = null;
        
        isFork = false;
        forkCount = 0;
        forkAngle = 30f;
        hasForked = false;
        
        hasAreaDamage = false;
        areaRadius = 0f;
        
        // Reset serpentine properties
        amplitude = waveAmplitude;
        frequency = waveFrequency;
        delay = serpentineDelay;
        _amplitudeMultiplier = 1f;
        
        
        // Ensure particles are stopped
        if (ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
    }
    
    // Support gem effect setters
    public void SetPiercing(bool enable, int count = 999)
    {
        isPiercing = enable;
        pierceCount = count;
        currentPierces = 0;
    }
    
    public void SetChaining(bool enable, int count = 3)
    {
        isChaining = enable;
        chainCount = count;
        currentChains = 0;
    }
    
    public void SetFork(bool enable, int count = 2, float angle = 30f)
    {
        isFork = enable;
        forkCount = count;
        forkAngle = angle;
        hasForked = false;
    }
    
    public void SetAreaDamage(bool enable, float radius = 2f)
    {
        hasAreaDamage = enable;
        areaRadius = radius;
    }

    // Debug helper methods
    public float GetCollisionRadius() => collisionRadius;
    public LayerMask GetCollisionLayerMask() => _currentLayerMask;
    
    // Serpentine-specific configuration
    public void SetSerpentineParameters(float waveAmplitude, float waveFrequency, float serpentineDelay)
    {
        amplitude = waveAmplitude;
        frequency = waveFrequency;
        delay = serpentineDelay;
    }

    /// <summary>
    /// Performance optimization: Limit collision checks for high frame rates
    /// </summary>
    private bool ShouldPerformCollisionCheck()
    {
        // Always check collision for fast projectiles or first few frames
        if (speed > 20f || _timeAlive < 0.1f)
        {
            return true;
        }

        // For slower projectiles, use frame-based limiting
        _framesSinceLastCheck++;
        if (_framesSinceLastCheck >= COLLISION_CHECK_FRAME_INTERVAL)
        {
            _framesSinceLastCheck = 0;
            return true;
        }

        return false;
    }
}