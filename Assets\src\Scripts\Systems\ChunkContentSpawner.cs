using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Collections;

public enum ChunkContentType
{
    None,
    En<PERSON>ies,
    Boss,
    TreasureChest,
    Breach,
    Shop
}

/// <summary>
/// System that spawns various content types in chunks when first visited.
/// Content is despawned when player moves too far away.
/// </summary>
public class ChunkContentSpawner : MonoBehaviour
{
    [Title("References")]
    [Required]
    [SerializeField] private TilemapChunkManager chunkManager;
    
    [Required]
    [SerializeField] private Transform playerTransform;
    
    [Title("Content Prefabs")]
    [Required]
    [AssetsOnly]
    [SerializeField] private GameObject bossPrefab;
    
    [Title("Enemy Race System")]
    [InfoBox("Race-based enemy spawning system with weighted selection")]
    [Required]
    [AssetsOnly]
    [SerializeField] private ScriptableObject enemySpawnConfig;
    
    [Required]
    [AssetsOnly]
    [SerializeField] private GameObject chestPrefab;
    
    [Title("Breach Prefabs")]
    [InfoBox("Breach prefabs that can spawn in chunks. Each should have BreachEnemySpawner component and configured splinter drops.")]
    [Required]
    [AssetsOnly]
    [SerializeField] private GameObject[] breachPrefabs;
    
    [Title("Shop Prefabs")]
    [InfoBox("Shop prefabs that can spawn in chunks. Each should have WorldShop + ShopTrigger components.")]
    [Required]
    [AssetsOnly]
    [SerializeField] private GameObject[] shopPrefabs;
    
    [Required]
    [SerializeField] private Camera mainCamera;
    
    [SerializeField]
    [Tooltip("Reference to ChunkBasedGraphMover to ensure pathfinding is ready")]
    private ChunkBasedGraphMover graphMover;
    
    [Title("Content Selection Strategy")]
    [SerializeField]
    private ContentSelectionStrategyManager contentStrategy = new ContentSelectionStrategyManager();
    
    [Title("Spawn Configuration")]
    [Required]
    [SerializeField]
    [Tooltip("ScriptableObject containing all spawn configuration settings")]
    private ChunkSpawnSettings spawnSettings;
    
    
    // Runtime data
    private HashSet<ChunkCoordinate> visitedChunks = new HashSet<ChunkCoordinate>();
    private Dictionary<ChunkCoordinate, List<GameObject>> spawnedContentPerChunk = new Dictionary<ChunkCoordinate, List<GameObject>>();
    private Dictionary<ChunkCoordinate, ChunkContentType> chunkContentTypes = new Dictionary<ChunkCoordinate, ChunkContentType>();
    private ChunkCoordinate currentPlayerChunk;
    private ChunkCoordinate startingChunk;
    private ChunkCoordinate lastBossChunk;
    private ChunkCoordinate lastChestChunk;
    private ChunkCoordinate lastBreachChunk;
    private ChunkCoordinate lastShopChunk;
    private float nextUpdateTime;
    private bool isInitialized = false;
    private int chunksSinceLastBoss = 0;
    private int chunksSinceLastChest = 0;
    private int chunksSinceLastBreach = 0;
    private int chunksSinceLastShop = 0;
    
    // Cached for performance - expensive lookups cached to avoid repeated FindObjectOfType calls
    private List<ChunkCoordinate> chunksToRemove = new List<ChunkCoordinate>();
    
    // Cached manager references (initialized once in Start)
    private PoolManager poolManagerCache;
    private TilemapChunkManager tilemapManagerCache;
    
    // Enemy despawning system state
    private CinemachineCameraBounds cameraBounds;
    private bool isCameraBoundsInitialized = false;
    private float lastTransitionEndTime = 0f;
    private float nextDespawningUpdate = 0f; // Now used to schedule despawn after chunk change

    // Race system caching (GC-free)
    private int currentChunkLevel = 1;
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalVisitedChunks => visitedChunks.Count;

    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalSpawnedContent
    {
        get
        {
            int count = 0;
            foreach (var list in spawnedContentPerChunk.Values)
            {
                count += list.Count;
            }
            return count;
        }
    }

    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private string despawningStatus
    {
        get
        {
            if (spawnSettings == null) return "Settings Missing";
            if (!spawnSettings.enableImmediateDespawning) return "Disabled";
            if (nextDespawningUpdate > 0f)
            {
                float timeUntilDespawn = nextDespawningUpdate - Time.time;
                if (timeUntilDespawn > 0)
                    return $"Scheduled (in {timeUntilDespawn:F1}s)";
                else
                    return "Processing...";
            }
            return "Idle (waiting for chunk change)";
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalBossesSpawned
    {
        get
        {
            int count = 0;
            foreach (var kvp in chunkContentTypes)
            {
                if (kvp.Value == ChunkContentType.Boss)
                    count++;
            }
            return count;
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalChestsSpawned
    {
        get
        {
            int count = 0;
            foreach (var kvp in chunkContentTypes)
            {
                if (kvp.Value == ChunkContentType.TreasureChest)
                    count++;
            }
            return count;
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalBreachesSpawned
    {
        get
        {
            int count = 0;
            foreach (var kvp in chunkContentTypes)
            {
                if (kvp.Value == ChunkContentType.Breach)
                    count++;
            }
            return count;
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalShopsSpawned
    {
        get
        {
            int count = 0;
            foreach (var kvp in chunkContentTypes)
            {
                if (kvp.Value == ChunkContentType.Shop)
                    count++;
            }
            return count;
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private string activeContentStrategy => contentStrategy?.GetCurrentStrategyName() ?? "Not Initialized";
    
    private void Start()
    {
        // Attempt to fallback to Camera.main if not assigned
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }

        // Check each required reference individually for better debugging
        bool hasErrors = false;
        
        if (chunkManager == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing chunkManager reference!");
            hasErrors = true;
        }
        
        if (playerTransform == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing playerTransform reference!");
            hasErrors = true;
        }
        
        if (bossPrefab == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing bossPrefab reference!");
            hasErrors = true;
        }
        
        if (chestPrefab == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing chestPrefab reference!");
            hasErrors = true;
        }
        
        if (breachPrefabs == null || breachPrefabs.Length == 0)
        {
            Debug.LogError("ChunkContentSpawner: Missing or empty breachPrefabs array!");
            hasErrors = true;
        }
        
        if (shopPrefabs == null || shopPrefabs.Length == 0)
        {
            Debug.LogError("ChunkContentSpawner: Missing or empty shopPrefabs array!");
            hasErrors = true;
        }
        
        if (mainCamera == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing mainCamera reference (Camera.main fallback also failed)!");
            hasErrors = true;
        }
        
        if (enemySpawnConfig == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing enemySpawnConfig reference!");
            hasErrors = true;
        }
        
        if (spawnSettings == null)
        {
            Debug.LogError("ChunkContentSpawner: Missing spawnSettings reference!");
            hasErrors = true;
        }
        
        if (hasErrors)
        {
            enabled = false;
            return;
        }
        
        // Get starting chunk (this chunk won't spawn agents)
        startingChunk = chunkManager.GetCurrentPlayerChunk();
        currentPlayerChunk = startingChunk;
        visitedChunks.Add(startingChunk);
        
        // Try to find graph mover if not assigned
        if (graphMover == null)
        {
            graphMover = GameObject.FindObjectOfType<ChunkBasedGraphMover>();
            if (graphMover == null)
            {
                Debug.LogWarning("ChunkContentSpawner: ChunkBasedGraphMover not found! Agents may spawn before pathfinding is ready.");
            }
            else if (spawnSettings.showDebugInfo)
            {
                Debug.Log("ChunkContentSpawner: Found ChunkBasedGraphMover automatically.");
            }
        }

        // Camera bounds will be lazily initialized when needed for immediate despawning

        // Cache manager references to avoid repeated FindObjectOfType calls
        CacheManagerReferences();
        
        // Initialize content selection strategy
        contentStrategy.Initialize();
        
        isInitialized = true;

        if (spawnSettings.showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Initialized. Starting chunk: {startingChunk}, Strategy: {contentStrategy.GetCurrentStrategyName()}");
        }
    }
    
    /// <summary>
    /// Caches expensive manager references to avoid repeated FindObjectOfType calls during runtime
    /// </summary>
    private void CacheManagerReferences()
    {
        // Cache PoolManager
        poolManagerCache = PoolManager.Instance;
        if (poolManagerCache == null && spawnSettings.showDebugInfo)
        {
            Debug.LogWarning("ChunkContentSpawner: PoolManager.Instance not found - spawning may fail");
        }
        
        // Cache TilemapChunkManager
        tilemapManagerCache = TilemapChunkManager.Instance;
        if (tilemapManagerCache == null && spawnSettings.showDebugInfo)
        {
            Debug.LogWarning("ChunkContentSpawner: TilemapChunkManager.Instance not found - chunk bounds may be incorrect");
        }
        
        
        if (spawnSettings.showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Cached manager references - Pool: {poolManagerCache != null}, Tilemap: {tilemapManagerCache != null}");
        }
    }
    
    private void Update()
    {
        if (!isInitialized || spawnSettings == null || Time.time < nextUpdateTime)
            return;

        nextUpdateTime = Time.time + spawnSettings.updateInterval;

        // Check current player chunk
        ChunkCoordinate playerChunk = chunkManager.GetCurrentPlayerChunk();

        // Check if player moved to a new chunk
        if (playerChunk.x != currentPlayerChunk.x || playerChunk.y != currentPlayerChunk.y)
        {
            ChunkCoordinate previousChunk = currentPlayerChunk;
            currentPlayerChunk = playerChunk;
            OnPlayerChunkChanged(playerChunk);
            
            // Trigger despawning only when player actually changes chunks
            if (spawnSettings.enableImmediateDespawning)
            {
                // Schedule immediate despawn check after potential camera transition
                nextDespawningUpdate = Time.time + spawnSettings.postTransitionDelay;
            }
            else
            {
                // Standard 3x3 grid despawning when chunk changes
                CheckAndDespawnDistantContent();
            }
        }
        
        // Only check for immediate despawning if it's scheduled (after chunk change)
        if (spawnSettings.enableImmediateDespawning && nextDespawningUpdate > 0f && Time.time >= nextDespawningUpdate)
        {
            // Check if we should still wait (camera might still be transitioning)
            if (IsTransitioning())
            {
                // Reschedule for later
                nextDespawningUpdate = Time.time + 0.1f;
                if (spawnSettings.showDebugInfo)
                {
                    Debug.Log("ChunkContentSpawner: Rescheduling despawn - camera still transitioning");
                }
            }
            else
            {
                nextDespawningUpdate = 0f; // Reset so it doesn't run again until next chunk change
                CheckAndDespawnDistantContentImmediate();
            }
        }
    }
    
    private void OnPlayerChunkChanged(ChunkCoordinate newChunk)
    {
        if (spawnSettings.showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Player moved to chunk {newChunk} (Immediate despawning: {(spawnSettings.enableImmediateDespawning ? "ON - will despawn after transition" : "OFF - using 3x3 grid")})");
        }
        
        // Don't spawn in the starting chunk
        if (newChunk.x == startingChunk.x && newChunk.y == startingChunk.y)
            return;
            
        // Check if this is a new chunk we haven't visited
        if (!visitedChunks.Contains(newChunk))
        {
            visitedChunks.Add(newChunk);
            chunksSinceLastBoss++;
            chunksSinceLastChest++;
            chunksSinceLastBreach++;
            chunksSinceLastShop++;
            StartCoroutine(SpawnContentInChunkWhenReady(newChunk));
        }
    }
    
    private ChunkContentType DetermineChunkContent()
    {
        // Create context for strategy using ScriptableObject settings
        var context = ContentSelectionContext.FromSettings(
            spawnSettings,
            chunksSinceLastBoss, chunksSinceLastChest, chunksSinceLastBreach, chunksSinceLastShop,
            currentPlayerChunk, startingChunk, visitedChunks.Count
        );
        
        // Use strategy to determine content
        ChunkContentType selectedContent = contentStrategy.DetermineContent(context);
        
        // Update counters based on selection
        var counterUpdates = contentStrategy.GetCounterUpdates(selectedContent);
        if (counterUpdates.resetBossCounter) chunksSinceLastBoss = 0;
        if (counterUpdates.resetShopCounter) chunksSinceLastShop = 0;
        if (counterUpdates.resetBreachCounter) chunksSinceLastBreach = 0;
        if (counterUpdates.resetChestCounter) chunksSinceLastChest = 0;
        
        return selectedContent;
    }
    
    private void SpawnContentInChunk(ChunkCoordinate chunk)
    {
        ChunkContentType contentType = DetermineChunkContent();
        chunkContentTypes[chunk] = contentType;
        
        if (contentType == ChunkContentType.None)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.Log($"ChunkContentSpawner: No content spawned in chunk {chunk}");
            }
            return;
        }
        
        Bounds chunkBounds = chunkManager.GetChunkBounds(chunk);
        List<GameObject> spawnedContent = new List<GameObject>();
        
        switch (contentType)
        {
            case ChunkContentType.Enemies:
                SpawnEnemies(chunk, chunkBounds, spawnedContent);
                break;
            case ChunkContentType.Boss:
                SpawnBoss(chunk, chunkBounds, spawnedContent);
                lastBossChunk = chunk;
                break;
            case ChunkContentType.TreasureChest:
                SpawnChest(chunk, chunkBounds, spawnedContent);
                lastChestChunk = chunk;
                break;
            case ChunkContentType.Breach:
                SpawnBreach(chunk, chunkBounds, spawnedContent);
                lastBreachChunk = chunk;
                break;
            case ChunkContentType.Shop:
                SpawnShop(chunk, chunkBounds, spawnedContent);
                lastShopChunk = chunk;
                break;
        }
        
        if (spawnedContent.Count > 0)
        {
            spawnedContentPerChunk[chunk] = spawnedContent;
        }
    }
    
    private void SpawnEnemies(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        SpawnEnemiesWithRaceSystem(chunk, chunkBounds, spawnedContent);
    }
    
    private void SpawnBoss(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        // Boss spawns in center of chunk
        Vector3 spawnPos = chunkBounds.center;
        
        // Only spawn if not visible
        if (!IsPositionVisibleToPlayer(spawnPos))
        {
            GameObject boss = poolManagerCache.Spawn(bossPrefab, spawnPos, Quaternion.identity);
            if (boss != null)
            {
                spawnedContent.Add(boss);
                
                // Set chunk bounds on the boss
                var bossMovement = boss.GetComponent<PathfindingMovement>();
                if (bossMovement != null)
                {
                    bossMovement.SetChunkBounds(chunkBounds);
                }
                
                if (spawnSettings.showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Spawned BOSS in chunk {chunk}");
                }
            }
        }
    }
    
    private void SpawnChest(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        // Chest spawns at a random position away from edges
        float margin = chunkBounds.size.x * 0.3f;
        Vector3 spawnPos = new Vector3(
            Random.Range(chunkBounds.min.x + margin, chunkBounds.max.x - margin),
            Random.Range(chunkBounds.min.y + margin, chunkBounds.max.y - margin),
            0f
        );
        
        // Only spawn if not visible
        if (!IsPositionVisibleToPlayer(spawnPos))
        {
            GameObject chest = poolManagerCache.Spawn(chestPrefab, spawnPos, Quaternion.identity);
            if (chest != null)
            {
                spawnedContent.Add(chest);
                
                if (spawnSettings.showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Spawned CHEST in chunk {chunk}");
                }
            }
        }
    }
    
    private void SpawnBreach(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        if (breachPrefabs == null || breachPrefabs.Length == 0)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.LogWarning($"ChunkContentSpawner: No breach prefabs configured for chunk {chunk}");
            }
            return;
        }
        
        // Choose random breach prefab (skip null entries)
        GameObject breachPrefab = null;
        int attempts = 0;
        while (breachPrefab == null && attempts < breachPrefabs.Length)
        {
            int randomIndex = Random.Range(0, breachPrefabs.Length);
            breachPrefab = breachPrefabs[randomIndex];
            attempts++;
        }
        
        if (breachPrefab == null)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.LogError($"ChunkContentSpawner: All breach prefabs are null! Check breachPrefabs array configuration.");
            }
            return;
        }
        
        // Breach spawns in center of chunk
        Vector3 spawnPos = chunkBounds.center;
        
        // Only spawn if not visible
        if (!IsPositionVisibleToPlayer(spawnPos))
        {
            GameObject breach = poolManagerCache.Spawn(breachPrefab, spawnPos, Quaternion.identity);
            if (breach != null)
            {
                spawnedContent.Add(breach);
                
                // Set up breach to register enemies with this spawner
                var breachSpawner = breach.GetComponent<BreachEnemySpawner>();
                if (breachSpawner != null)
                {
                    // The breach will register its spawned enemies with this chunk
                    SetupBreachEnemyTracking(breachSpawner, chunk);
                    
                    if (spawnSettings.showDebugInfo)
                    {
                        Debug.Log($"ChunkContentSpawner: Spawned {breachPrefab.name} BREACH in chunk {chunk} (using prefab configuration)");
                    }
                }
                else
                {
                    if (spawnSettings.showDebugInfo)
                    {
                        Debug.LogWarning($"ChunkContentSpawner: Breach prefab {breachPrefab.name} missing BreachEnemySpawner component!");
                    }
                }
            }
        }
    }
    
    private void SpawnShop(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        if (shopPrefabs == null || shopPrefabs.Length == 0)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.LogWarning($"ChunkContentSpawner: No shop prefabs configured for chunk {chunk}");
            }
            return;
        }
        
        // Choose random shop prefab
        GameObject shopPrefab = shopPrefabs[Random.Range(0, shopPrefabs.Length)];
        
        // Shop spawns at a random position away from edges (similar to chest)
        float margin = chunkBounds.size.x * 0.3f;
        Vector3 spawnPos = new Vector3(
            Random.Range(chunkBounds.min.x + margin, chunkBounds.max.x - margin),
            Random.Range(chunkBounds.min.y + margin, chunkBounds.max.y - margin),
            0f
        );
        
        // Only spawn if not visible
        if (!IsPositionVisibleToPlayer(spawnPos))
        {
            GameObject shop = poolManagerCache.Spawn(shopPrefab, spawnPos, Quaternion.identity);
            if (shop != null)
            {
                spawnedContent.Add(shop);
                
                // Initialize shop trigger and world shop components
                var shopTrigger = shop.GetComponent<ShopTrigger>();
                var worldShop = shop.GetComponent<WorldShop>();
                
                if (shopTrigger != null && worldShop != null)
                {
                    // Preserve prefab's pre-configured shop type and settings
                    // No override - let the prefab keep its configured values
                    
                    // Link shop trigger to world shop
                    shopTrigger.SetShop(worldShop);
                    
                    if (spawnSettings.showDebugInfo)
                    {
                        Debug.Log($"ChunkContentSpawner: Spawned {worldShop.GetShopType()} SHOP in chunk {chunk} (using prefab configuration)");
                    }
                }
                else
                {
                    if (spawnSettings.showDebugInfo)
                    {
                        Debug.LogWarning($"ChunkContentSpawner: Shop prefab {shopPrefab.name} missing ShopTrigger or WorldShop component!");
                    }
                }
            }
        }
    }
    
    private Vector3 FindValidSpawnPosition(Bounds chunkBounds, Vector3 chunkCenter, Vector3 dirFromPlayer)
    {
        const int MaxAttempts = 15;
        int attempts = 0;
        
        // Calculate offset center based on player approach direction
        // dirFromPlayer points from player to chunk center, we want to offset in that direction
        float offsetDistance = Mathf.Min(chunkBounds.size.x, chunkBounds.size.y) * 0.25f; // Offset by 25% of chunk size
        Vector3 offsetCenter = chunkCenter + (dirFromPlayer * offsetDistance);
        
        // Clamp offset center to stay within chunk bounds (with margin)
        float margin = spawnSettings.spawnMargin * 2f;
        offsetCenter.x = Mathf.Clamp(offsetCenter.x, chunkBounds.min.x + margin, chunkBounds.max.x - margin);
        offsetCenter.y = Mathf.Clamp(offsetCenter.y, chunkBounds.min.y + margin, chunkBounds.max.y - margin);
        
        while (attempts < MaxAttempts)
        {
            attempts++;
            Vector3 spawnPos;
            
            // Spawn around the offset center (away from player entry)
            float spawnRadius = 0.2f; // Spawn within 20% of chunk size from offset center
            float randomAngle = Random.Range(0f, Mathf.PI * 2f);
            float randomDistance = Random.Range(0f, Mathf.Min(chunkBounds.size.x, chunkBounds.size.y) * spawnRadius);
            
            spawnPos = offsetCenter + new Vector3(
                Mathf.Cos(randomAngle) * randomDistance,
                Mathf.Sin(randomAngle) * randomDistance,
                0f
            );
            
            // Ensure position is within chunk bounds
            spawnPos.x = Mathf.Clamp(spawnPos.x, chunkBounds.min.x + spawnSettings.spawnMargin, chunkBounds.max.x - spawnSettings.spawnMargin);
            spawnPos.y = Mathf.Clamp(spawnPos.y, chunkBounds.min.y + spawnSettings.spawnMargin, chunkBounds.max.y - spawnSettings.spawnMargin);
            
            // Ensure the position is not currently visible by the player's camera
            if (!IsPositionVisibleToPlayer(spawnPos))
            {
                return spawnPos;
            }
        }
        
        // Fallback: Use offset center directly
        if (spawnSettings.showDebugInfo)
        {
            Debug.LogWarning($"ChunkContentSpawner: Using fallback spawn position at offset center.");
        }
        
        return offsetCenter;
    }
    
    // Returns true if the given world position is currently inside the camera's viewport (with a small margin)
    private bool IsPositionVisibleToPlayer(Vector3 worldPos)
    {
        if (mainCamera == null) return false;
        Vector3 viewportPoint = mainCamera.WorldToViewportPoint(worldPos);
        // The point is visible if it lies within the [0,1] range on x and y and is in front of the camera (z>0)
        return viewportPoint.z > 0f && viewportPoint.x >= 0f && viewportPoint.x <= 1f && viewportPoint.y >= 0f && viewportPoint.y <= 1f;
    }
    
    private void CheckAndDespawnDistantContent()
    {
        chunksToRemove.Clear();
        
        // With 3x3 grid system, despawn content outside the 3x3 area
        foreach (var kvp in spawnedContentPerChunk)
        {
            ChunkCoordinate chunk = kvp.Key;
            
            // Calculate distance from current player chunk
            int distanceX = Mathf.Abs(chunk.x - currentPlayerChunk.x);
            int distanceY = Mathf.Abs(chunk.y - currentPlayerChunk.y);
            int maxDistance = Mathf.Max(distanceX, distanceY);
            
            // Despawn if outside 3x3 grid (distance > 1)
            if (maxDistance > 1)
            {
                chunksToRemove.Add(chunk);
            }
        }
        
        // Despawn content in distant chunks
        foreach (var chunk in chunksToRemove)
        {
            DespawnContentInChunk(chunk);
        }
    }

    /// <summary>
    /// Ensures camera bounds reference is initialized using lazy initialization
    /// </summary>
    private void EnsureCameraBoundsInitialized()
    {
        if (!isCameraBoundsInitialized)
        {
            cameraBounds = GameObject.FindObjectOfType<CinemachineCameraBounds>();
            isCameraBoundsInitialized = true;
            
            if (spawnSettings.showDebugInfo)
            {
                if (cameraBounds != null)
                {
                    Debug.Log("ChunkContentSpawner: Lazily initialized CinemachineCameraBounds for transition detection.");
                }
                else
                {
                    Debug.LogWarning("ChunkContentSpawner: CinemachineCameraBounds not found! Immediate despawning will not detect transitions.");
                }
            }
        }
    }
    
    /// <summary>
    /// Checks if camera is currently transitioning between chunks
    /// </summary>
    private bool IsTransitioning()
    {
        if (spawnSettings.enableImmediateDespawning)
        {
            EnsureCameraBoundsInitialized();
        }
        return cameraBounds != null && cameraBounds.IsTransitioning;
    }

    /// <summary>
    /// Immediate despawning system - despawns enemies when player leaves their chunk
    /// </summary>
    private void CheckAndDespawnDistantContentImmediate()
    {
        // Don't despawn during camera transitions
        if (IsTransitioning())
        {
            if (spawnSettings.showDebugInfo)
                Debug.Log("ChunkContentSpawner: Skipping immediate despawning - camera is transitioning");
            return;
        }

        // Don't despawn immediately after transition ends
        if (Time.time < lastTransitionEndTime + spawnSettings.postTransitionDelay)
        {
            if (spawnSettings.showDebugInfo)
                Debug.Log("ChunkContentSpawner: Skipping immediate despawning - post-transition delay");
            return;
        }

        // Track when transitions end
        if (cameraBounds != null)
        {
            bool currentlyTransitioning = cameraBounds.IsTransitioning;
            
            // If we just finished transitioning, record the time
            if (!currentlyTransitioning && lastTransitionEndTime < Time.time - 1f)
            {
                lastTransitionEndTime = Time.time;
                if (spawnSettings.showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Camera transition ended, waiting {spawnSettings.postTransitionDelay}s before despawning");
                }
            }
        }

        chunksToRemove.Clear();

        // Immediate despawning: despawn content in any chunk that isn't the current player chunk
        foreach (var kvp in spawnedContentPerChunk)
        {
            ChunkCoordinate chunk = kvp.Key;

            // Despawn if not in current player chunk (distance > 0)
            if (chunk.x != currentPlayerChunk.x || chunk.y != currentPlayerChunk.y)
            {
                chunksToRemove.Add(chunk);
            }
        }

        // Despawn content in distant chunks
        int despawnedChunks = 0;
        foreach (var chunk in chunksToRemove)
        {
            DespawnContentInChunk(chunk);
            despawnedChunks++;
        }

        if (despawnedChunks > 0)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.Log($"ChunkContentSpawner: Immediate despawning removed enemies from {despawnedChunks} chunks (player now in {currentPlayerChunk})");
            }
        }
        else if (spawnSettings.showDebugInfo && spawnedContentPerChunk.Count > 1)
        {
            Debug.Log($"ChunkContentSpawner: Immediate despawn check complete. Player in {currentPlayerChunk}, {spawnedContentPerChunk.Count} chunks still have content");
        }
    }

    private void DespawnContentInChunk(ChunkCoordinate chunk)
    {
        // NEW: Centralized breach enemy cleanup FIRST
        // This ensures breach enemies are cleaned up through the centralized system
        // before we despawn regular chunk content, preventing race conditions
        if (BreachEnemyManager.Instance != null)
        {
            BreachEnemyManager.Instance.DespawnEnemiesInChunk(chunk);
            
            if (spawnSettings.showDebugInfo)
            {
                Debug.Log($"ChunkContentSpawner: Centralized breach enemy cleanup completed for chunk {chunk}");
            }
        }
        else if (spawnSettings.showDebugInfo)
        {
            Debug.LogWarning($"ChunkContentSpawner: BreachEnemyManager.Instance not available for chunk {chunk} cleanup");
        }
        
        // EXISTING: Regular content cleanup continues as before
        if (!spawnedContentPerChunk.ContainsKey(chunk))
            return;
            
        var content = spawnedContentPerChunk[chunk];
        int despawnCount = 0;
        for (int i = content.Count - 1; i >= 0; i--)
        {
            var obj = content[i];
            if (obj != null)
            {
                poolManagerCache.Despawn(obj);
                despawnCount++;
            }
        }
        
        content.Clear();
        spawnedContentPerChunk.Remove(chunk);
        
        if (spawnSettings.showDebugInfo)
        {
            ChunkContentType contentType = chunkContentTypes.GetValueOrDefault(chunk, ChunkContentType.None);
            Debug.Log($"ChunkContentSpawner: Despawned {despawnCount} {contentType} from chunk {chunk}");
        }
    }
    
    /// <summary>
    /// Force immediate despawn of all content in a chunk (called by TilemapChunkManager)
    /// </summary>
    public void ForceChunkContentDespawn(ChunkCoordinate chunk)
    {
        DespawnContentInChunk(chunk);
    }
    
    private IEnumerator SpawnContentInChunkWhenReady(ChunkCoordinate chunk)
    {
        // Wait for graph mover to be ready if it exists
        if (graphMover != null)
        {
            // Wait a frame to let graph mover update first
            yield return null;
            
            // Wait while graph is scanning
            if (graphMover.IsScanning())
            {
                if (spawnSettings.showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Waiting for pathfinding graph to be ready for chunk {chunk}...");
                }
                
                while (graphMover.IsScanning())
                {
                    yield return new WaitForSeconds(0.1f);
                }
                
                // Small additional delay to ensure graph is fully ready
                yield return new WaitForSeconds(0.1f);
                
                if (spawnSettings.showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Pathfinding graph ready, spawning content in chunk {chunk}");
                }
            }
        }
        
        SpawnContentInChunk(chunk);
    }
    
    #region Race System Methods
    
    /// <summary>
    /// Spawns enemies using the race system with weighted selection
    /// </summary>
    private void SpawnEnemiesWithRaceSystem(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        // Calculate chunk level (distance from starting chunk)
        int chunkLevel = CalculateChunkLevel(chunk);
        
        // Try to cast the ScriptableObject to EnemySpawnConfiguration
        var spawnConfig = enemySpawnConfig as EnemySpawnConfiguration;
        if (spawnConfig == null)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.LogError("ChunkContentSpawner: enemySpawnConfig is not of type EnemySpawnConfiguration!");
            }
            return;
        }
        
        // Check if enemies should spawn at all
        if (!spawnConfig.ShouldSpawnEnemies())
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.Log($"ChunkContentSpawner: Skipped enemy spawn in chunk {chunk} due to spawn chance");
            }
            return;
        }
        
        // Get available races for this chunk level
        var availableRaces = spawnConfig.GetAvailableRaces(chunkLevel);
        if (availableRaces == null || availableRaces.Length == 0)
        {
            if (spawnSettings.showDebugInfo)
            {
                Debug.LogWarning($"ChunkContentSpawner: No races available for chunk level {chunkLevel} in chunk {chunk}");
            }
            return;
        }
        
        // Determine enemy count with scaling
        int enemyCount = spawnConfig.GetScaledEnemyCount(chunkLevel, CalculateDistanceFromSpawn(chunk));
        
        // Spawn enemies based on race selection
        SpawnEnemiesFromRaces(chunk, chunkBounds, spawnedContent, availableRaces, enemyCount, spawnConfig);
        
        if (spawnSettings.showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Spawned {spawnedContent.Count} enemies from {availableRaces.Length} available races in chunk {chunk} (level {chunkLevel})");
        }
    }
    
    /// <summary>
    /// Spawns enemies from selected races
    /// </summary>
    private void SpawnEnemiesFromRaces(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent, 
        WeightedEnemyRace[] availableRaces, int enemyCount, EnemySpawnConfiguration spawnConfig)
    {
        Vector3 playerPos = playerTransform.position;
        Vector3 chunkCenter = chunkBounds.center;
        Vector3 dirFromPlayer = (chunkCenter - playerPos).normalized;
        
        int spawnedCount = 0;
        bool shouldSpawnMultipleRaces = spawnConfig.ShouldSpawnMultipleRaces() && availableRaces.Length > 1;
        
        while (spawnedCount < enemyCount)
        {
            // Select race using simple weighted selection
            WeightedEnemyRace selectedRace = SelectRaceWeighted(availableRaces);
            if (selectedRace?.RaceData == null)
                break;
                
            // Get available enemy types for current chunk level
            int chunkLevel = CalculateChunkLevel(chunk);
            var availableTypes = selectedRace.RaceData.GetAvailableEnemyTypes(chunkLevel);
            if (availableTypes == null || availableTypes.Length == 0)
                continue;
                
            // Determine how many enemies to spawn from this race
            int racialSpawnCount = shouldSpawnMultipleRaces ? 
                Random.Range(selectedRace.RaceData.MinEnemiesPerSpawn, selectedRace.RaceData.MaxEnemiesPerSpawn + 1) :
                enemyCount - spawnedCount;
                
            racialSpawnCount = Mathf.Min(racialSpawnCount, enemyCount - spawnedCount);
            
            // Spawn enemies from this race
            for (int i = 0; i < racialSpawnCount && spawnedCount < enemyCount; i++)
            {
                WeightedEnemyType selectedType = SelectEnemyTypeWeighted(availableTypes);
                if (selectedType?.Prefab == null)
                    continue;
                    
                Vector3 spawnPos = FindValidSpawnPosition(chunkBounds, chunkCenter, dirFromPlayer);
                if (spawnPos != Vector3.zero)
                {
                    GameObject enemy = poolManagerCache.Spawn(selectedType.Prefab, spawnPos, Quaternion.identity);
                    if (enemy != null)
                    {
                        spawnedContent.Add(enemy);
                        spawnedCount++;
                        
                        // Set chunk bounds on the enemy
                        var pathfindingMovement = enemy.GetComponent<PathfindingMovement>();
                        if (pathfindingMovement != null)
                        {
                            pathfindingMovement.SetChunkBounds(chunkBounds);
                        }
                    }
                }
            }
            
            // If not spawning multiple races, exit after first race
            if (!shouldSpawnMultipleRaces)
                break;
        }
    }
    
    /// <summary>
    /// Simple weighted race selection without GC allocation
    /// </summary>
    private WeightedEnemyRace SelectRaceWeighted(WeightedEnemyRace[] races)
    {
        if (races == null || races.Length == 0)
            return null;
            
        float totalWeight = 0f;
        foreach (var race in races)
        {
            if (race != null && race.Weight > 0f)
                totalWeight += race.Weight;
        }
        
        if (totalWeight <= 0f)
            return races[0]; // Fallback to first race
            
        float randomValue = Random.value * totalWeight;
        float currentWeight = 0f;
        
        foreach (var race in races)
        {
            if (race != null && race.Weight > 0f)
            {
                currentWeight += race.Weight;
                if (randomValue <= currentWeight)
                    return race;
            }
        }
        
        return races[races.Length - 1]; // Fallback to last race
    }
    
    /// <summary>
    /// Simple weighted enemy type selection without GC allocation
    /// </summary>
    private WeightedEnemyType SelectEnemyTypeWeighted(WeightedEnemyType[] types)
    {
        if (types == null || types.Length == 0)
            return null;
            
        float totalWeight = 0f;
        foreach (var type in types)
        {
            if (type != null && type.Weight > 0f)
                totalWeight += type.Weight;
        }
        
        if (totalWeight <= 0f)
            return types[0]; // Fallback to first type
            
        float randomValue = Random.value * totalWeight;
        float currentWeight = 0f;
        
        foreach (var type in types)
        {
            if (type != null && type.Weight > 0f)
            {
                currentWeight += type.Weight;
                if (randomValue <= currentWeight)
                    return type;
            }
        }
        
        return types[types.Length - 1]; // Fallback to last type
    }
    
    /// <summary>
    /// Calculates the level of a chunk based on distance from starting chunk
    /// </summary>
    private int CalculateChunkLevel(ChunkCoordinate chunk)
    {
        int deltaX = Mathf.Abs(chunk.x - startingChunk.x);
        int deltaY = Mathf.Abs(chunk.y - startingChunk.y);
        return Mathf.Max(deltaX, deltaY) + 1; // Manhattan distance + 1 (so starting chunk is level 1)
    }
    
    /// <summary>
    /// Calculates the Manhattan distance from starting chunk
    /// </summary>
    private int CalculateDistanceFromSpawn(ChunkCoordinate chunk)
    {
        int deltaX = Mathf.Abs(chunk.x - startingChunk.x);
        int deltaY = Mathf.Abs(chunk.y - startingChunk.y);
        return deltaX + deltaY;
    }
    
    #endregion
    
    #region Breach Enemy Tracking
    /// <summary>
    /// Sets up a breach to register its spawned enemies with this chunk spawner
    /// </summary>
    private void SetupBreachEnemyTracking(BreachEnemySpawner breachSpawner, ChunkCoordinate chunk)
    {
        // BreachEnemySpawner now uses centralized BreachEnemyManager for tracking
        // No manual registration needed - handled automatically
        if (spawnSettings.showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Breach enemy tracking enabled for chunk {chunk} - BreachEnemyManager handles centralized cleanup");
        }
    }
    
    /// <summary>
    /// DEPRECATED: This method is no longer used due to race condition fixes.
    /// BreachEnemySpawner now maintains sole ownership of its spawned enemies.
    /// Was previously used to register breach enemies for chunk-based cleanup,
    /// but caused dual-tracking race conditions leading to "not a pooled object" errors.
    /// 
    /// MIGRATION STATUS: This method is now a no-op to prevent interference with
    /// the centralized BreachEnemyManager system. All breach enemy tracking is
    /// handled by BreachEnemyManager.Instance.
    /// </summary>
    [System.Obsolete("Deprecated: BreachEnemySpawner now handles its own cleanup to prevent race conditions")]
    public void RegisterBreachSpawnedEnemy(GameObject enemy, ChunkCoordinate chunk)
    {
        // NO-OP: Early return to prevent interference with centralized system
        if (spawnSettings.showDebugInfo)
        {
            Debug.LogWarning($"ChunkContentSpawner: DEPRECATED RegisterBreachSpawnedEnemy() called for {enemy?.name} in chunk {chunk} - this method is no longer functional. Use BreachEnemyManager.Instance.RegisterBreachEnemy() instead.");
        }
        
        return; // Centralized BreachEnemyManager now handles all breach enemy tracking
    }
    
    /// <summary>
    /// DEPRECATED: This method is no longer used due to race condition fixes.
    /// BreachEnemySpawner now maintains sole ownership of its spawned enemies.
    /// Was previously used to unregister breach enemies during manual cleanup,
    /// but caused dual-tracking race conditions leading to "not a pooled object" errors.
    /// 
    /// MIGRATION STATUS: This method is now a no-op to prevent interference with
    /// the centralized BreachEnemyManager system. All breach enemy tracking is
    /// handled by BreachEnemyManager.Instance.
    /// </summary>
    [System.Obsolete("Deprecated: BreachEnemySpawner now handles its own cleanup to prevent race conditions")]
    public void UnregisterBreachSpawnedEnemy(GameObject enemy, ChunkCoordinate chunk)
    {
        // NO-OP: Early return to prevent interference with centralized system
        if (spawnSettings.showDebugInfo)
        {
            Debug.LogWarning($"ChunkContentSpawner: DEPRECATED UnregisterBreachSpawnedEnemy() called for {enemy?.name} in chunk {chunk} - this method is no longer functional. Use BreachEnemyManager.Instance.UnregisterBreachEnemy() instead.");
        }
        
        return; // Centralized BreachEnemyManager now handles all breach enemy tracking
    }
    #endregion
    
    private void OnDestroy()
    {
        // Clean up all spawned content
        foreach (var kvp in spawnedContentPerChunk)
        {
            foreach (var obj in kvp.Value)
            {
                if (obj != null && poolManagerCache != null)
                {
                    poolManagerCache.Despawn(obj);
                }
            }
        }
        
        spawnedContentPerChunk.Clear();
        chunkContentTypes.Clear();
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!isInitialized || !spawnSettings.showDebugInfo)
            return;
            
        // Draw current player chunk
        Gizmos.color = Color.green;
        DrawChunkGizmo(currentPlayerChunk);
        
        // Draw chunks with content
        foreach (var kvp in chunkContentTypes)
        {
            switch (kvp.Value)
            {
                case ChunkContentType.Enemies:
                    Gizmos.color = Color.yellow;
                    break;
                case ChunkContentType.Boss:
                    Gizmos.color = Color.red;
                    break;
                case ChunkContentType.TreasureChest:
                    Gizmos.color = Color.cyan;
                    break;
                case ChunkContentType.Breach:
                    Gizmos.color = Color.magenta;
                    break;
                case ChunkContentType.Shop:
                    Gizmos.color = Color.blue;
                    break;
                default:
                    continue;
            }
            DrawChunkGizmo(kvp.Key);
        }
        
        // Draw spawn positions
        foreach (var kvp in spawnedContentPerChunk)
        {
            ChunkContentType contentType = chunkContentTypes.GetValueOrDefault(kvp.Key, ChunkContentType.None);
            
            switch (contentType)
            {
                case ChunkContentType.Enemies:
                    Gizmos.color = Color.yellow;
                    break;
                case ChunkContentType.Boss:
                    Gizmos.color = Color.red;
                    break;
                case ChunkContentType.TreasureChest:
                    Gizmos.color = Color.cyan;
                    break;
                case ChunkContentType.Breach:
                    Gizmos.color = Color.magenta;
                    break;
                case ChunkContentType.Shop:
                    Gizmos.color = Color.blue;
                    break;
            }
            
            foreach (var obj in kvp.Value)
            {
                if (obj != null)
                {
                    float size = contentType == ChunkContentType.Boss ? 1f : 0.5f;
                    Gizmos.DrawWireSphere(obj.transform.position, size);
                }
            }
        }
    }
    
    private void DrawChunkGizmo(ChunkCoordinate chunk)
    {
        if (chunkManager == null) return;
        
        Bounds bounds = chunkManager.GetChunkBounds(chunk);
        Gizmos.DrawWireCube(bounds.center, bounds.size);
    }
    
    [Button("Debug: Show Spawned Content Status")]
    public void DebugSpawnedContent()
    {
        if (!Application.isPlaying)
        {
            Debug.Log("Content Status: Only available in play mode");
            return;
        }
        
        Debug.Log("=== SPAWNED CONTENT STATUS ===");
        Debug.Log($"Despawn Mode: {(spawnSettings.enableImmediateDespawning ? "IMMEDIATE (only current chunk)" : "3x3 GRID")}");
        Debug.Log($"Current Player Chunk: {currentPlayerChunk}");
        Debug.Log($"Total Chunks with Content: {spawnedContentPerChunk.Count}");
        
        int totalEnemies = 0;
        int totalBosses = 0;
        int totalChests = 0;
        int totalBreaches = 0;
        int totalShops = 0;
        
        foreach (var kvp in spawnedContentPerChunk)
        {
            ChunkCoordinate chunk = kvp.Key;
            List<GameObject> content = kvp.Value;
            ChunkContentType type = chunkContentTypes.GetValueOrDefault(chunk, ChunkContentType.None);
            
            // Calculate distance from player
            int dx = Mathf.Abs(chunk.x - currentPlayerChunk.x);
            int dy = Mathf.Abs(chunk.y - currentPlayerChunk.y);
            int distance = Mathf.Max(dx, dy);
            
            string status;
            if (spawnSettings.enableImmediateDespawning)
            {
                status = distance == 0 ? "CURRENT CHUNK (OK)" : "NOT CURRENT CHUNK (should be despawned!)";
            }
            else
            {
                status = distance <= 1 ? "WITHIN 3x3 (OK)" : "OUTSIDE 3x3 (should be despawned!)";
            }
            
            Debug.Log($"  Chunk {chunk}: {content.Count} objects ({type}) - Distance: {distance} - {status}");
            
            // Count by type
            switch (type)
            {
                case ChunkContentType.Enemies:
                    totalEnemies += content.Count;
                    break;
                case ChunkContentType.Boss:
                    totalBosses += content.Count;
                    break;
                case ChunkContentType.TreasureChest:
                    totalChests += content.Count;
                    break;
                case ChunkContentType.Breach:
                    totalBreaches += content.Count;
                    break;
                case ChunkContentType.Shop:
                    totalShops += content.Count;
                    break;
            }
            
            // Warn based on despawn mode
            if (spawnSettings.enableImmediateDespawning && distance > 0)
            {
                Debug.LogWarning($"WARNING: Content still exists in chunk {chunk} (not current chunk)!");
            }
            else if (!spawnSettings.enableImmediateDespawning && distance > 1)
            {
                Debug.LogWarning($"WARNING: Content still exists in chunk {chunk} outside 3x3 grid!");
            }
        }
        
        Debug.Log($"\nTotal Spawned: {totalEnemies} enemies, {totalBosses} bosses, {totalChests} chests, {totalBreaches} breaches, {totalShops} shops");
        Debug.Log("=============================");
    }

#if UNITY_EDITOR
    [Button("Test Immediate Despawning", ButtonSizes.Medium)]
    private void TestImmediateDespawning()
    {
        if (!Application.isPlaying || !isInitialized)
        {
            Debug.LogWarning("ChunkContentSpawner: Can only test immediate despawning during play mode when initialized.");
            return;
        }

        Debug.Log("=== Testing Immediate Despawning ===");
        Debug.Log($"Current player chunk: {currentPlayerChunk}");
        Debug.Log($"Transition status: {(IsTransitioning() ? "Transitioning" : "Not transitioning")}");
        Debug.Log($"Post-transition delay active: {(Time.time < lastTransitionEndTime + spawnSettings.postTransitionDelay)}");

        int chunksBeforeDespawn = spawnedContentPerChunk.Count;
        CheckAndDespawnDistantContentImmediate();
        int chunksAfterDespawn = spawnedContentPerChunk.Count;

        Debug.Log($"Chunks with content before: {chunksBeforeDespawn}, after: {chunksAfterDespawn}");
        Debug.Log("=== Test Complete ===");
    }

    [Button("Toggle Immediate Despawning", ButtonSizes.Medium)]
    private void ToggleImmediateDespawning()
    {
        spawnSettings.enableImmediateDespawning = !spawnSettings.enableImmediateDespawning;
        Debug.Log($"Immediate despawning: {(spawnSettings.enableImmediateDespawning ? "ENABLED" : "DISABLED")}");

        if (spawnSettings.enableImmediateDespawning && Application.isPlaying && cameraBounds == null)
        {
            cameraBounds = GameObject.FindObjectOfType<CinemachineCameraBounds>();
            if (cameraBounds == null)
            {
                Debug.LogWarning("CinemachineCameraBounds not found! Transition detection will not work.");
            }
        }
    }
#endif
}