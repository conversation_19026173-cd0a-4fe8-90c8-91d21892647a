using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

public class PlayerBuffSystem : MonoBehaviour
{
    private static PlayerBuffSystem instance;
    public static PlayerBuffSystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindFirstObjectByType<PlayerBuffSystem>();
            }
            return instance;
        }
    }
    
    [Title("Active Buffs")]
    [ShowInInspector]
    [ReadOnly]
    private List<PlayerBuffData> activeBuffs = new List<PlayerBuffData>();
    
    [Title("Modifier Collection")]
    [ShowInInspector]
    private StatModifierCollection buffModifiers = new StatModifierCollection();
    
    [Title("References")]
    [Required]
    public PlayerStats playerStats;
    
    // Cache for nearby enemies to eliminate allocations in collision detection
    private readonly List<ICollidable> _nearbyEnemiesCache = new List<ICollidable>(32);
    
    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        if (playerStats == null)
        {
            playerStats = PlayerManager.PlayerStats;
            if (playerStats == null)
            {
                playerStats = FindFirstObjectByType<PlayerStats>();
            }
        }
    }
    
    /// <summary>
    /// Add a permanent buff to the player
    /// </summary>
    public void AddBuff(PlayerBuffData buff)
    {
        if (buff == null) return;
        
        activeBuffs.Add(buff);
        
        // Add all modifiers from this buff
        foreach (var modifier in buff.modifiers)
        {
            // Create a copy with the buff name as source
            var buffModifier = new StatModifier(modifier.statType, modifier.modifierType, modifier.value, buff.buffName);
            buffModifiers.Add(buffModifier);
        }
        
        Debug.Log($"[PlayerBuffSystem] Added buff: {buff.buffName}");
        
        // Notify UI or other systems
        OnBuffsChanged();
        
        // Trigger cache invalidation event
        SkillCacheEvents.TriggerPlayerBuffsChanged();
    }
    
    /// <summary>
    /// Remove a buff and its modifiers
    /// </summary>
    public void RemoveBuff(PlayerBuffData buff)
    {
        if (buff == null || !activeBuffs.Contains(buff)) return;
        
        activeBuffs.Remove(buff);
        
        // Remove all modifiers from this buff
        buffModifiers.RemoveAllFromSource(buff.buffName);
        
        Debug.Log($"[PlayerBuffSystem] Removed buff: {buff.buffName}");
        
        // Notify UI or other systems
        OnBuffsChanged();
        
        // Trigger cache invalidation event
        SkillCacheEvents.TriggerPlayerBuffsChanged();
    }
    
    /// <summary>
    /// Get the modifier collection for stat calculations
    /// </summary>
    public StatModifierCollection GetBuffModifiers()
    {
        return buffModifiers;
    }
    
    /// <summary>
    /// Get all active buffs
    /// </summary>
    public List<PlayerBuffData> GetActiveBuffs()
    {
        return new List<PlayerBuffData>(activeBuffs);
    }
    
    /// <summary>
    /// Check if a specific buff is active
    /// </summary>
    public bool HasBuff(PlayerBuffData buff)
    {
        return activeBuffs.Contains(buff);
    }
    
    /// <summary>
    /// Handle special buff mechanics
    /// </summary>
    public void OnEnemyKilled(GameObject enemy)
    {
        foreach (var buff in activeBuffs)
        {
            // Life on kill
            if (buff.grantsLifeOnKill)
            {
                PlayerManager.HealPlayer(buff.lifeOnKillAmount);
            }
            
            // Mana on kill
            if (buff.grantsManaOnKill && playerStats != null)
            {
                playerStats.RestoreMana(buff.manaOnKillAmount);
            }
            
            // Explosion on kill
            if (buff.grantsExplosionOnKill && enemy != null)
            {
                CreateExplosion(enemy.transform.position, buff.explosionRadius, buff.explosionDamage);
            }
        }
    }
    
    private void CreateExplosion(Vector3 position, float radius, int damage)
    {
        // Visual effect (optional)
        // Could spawn explosion particle effect here
        
        // Deal damage to nearby enemies using Physics2D
        Collider2D[] colliders = Physics2D.OverlapCircleAll(position, radius);
        
        // Create damage info for explosion - currently Physical damage
        // TODO: Consider adding explosionDamageType to PlayerBuffData for elemental explosions
        var damageInfo = DamageInfo.FromSingleType(
            damage, 
            DamageType.Physical, 
            false, // Not a critical hit
            1f,    // No crit multiplier
            "BuffExplosion"
        );
        
        foreach (var collider in colliders)
        {
            if (collider.CompareTag("Enemy"))
            {
                if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(collider.gameObject, out var health))
                {
                    health.TakeDamage(damageInfo);
                }
            }
        }
    }
    
    private void OnBuffsChanged()
    {
        // Could fire events here for UI updates
        // OnBuffsUpdated?.Invoke(activeBuffs);
    }
    
    // Debug methods
    [Title("Debug")]
    [Button("Clear All Buffs")]
    private void ClearAllBuffs()
    {
        activeBuffs.Clear();
        buffModifiers.modifiers.Clear();
        OnBuffsChanged();
    }
    
    [Button("Log Active Buffs")]
    private void LogActiveBuffs()
    {
        Debug.Log($"[PlayerBuffSystem] Active Buffs: {activeBuffs.Count}");
        foreach (var buff in activeBuffs)
        {
            Debug.Log($"  - {buff.buffName} ({buff.modifiers.Count} modifiers)");
        }
    }
}