using UnityEngine;

/// <summary>
/// Interface for objects that handle manual collision detection using Unity's Raycast system.
/// Used to replace Unity's automatic collision events (OnTriggerEnter2D, OnCollisionEnter2D)
/// with manual collision processing in FixedUpdate.
/// </summary>
public interface ICollisionHandler
{
    /// <summary>
    /// Handle a collision event detected by manual raycast/overlap checking.
    /// Called when a collision is detected during manual collision detection.
    /// </summary>
    /// <param name="collider">The collider that was hit</param>
    /// <param name="hitPoint">The world position where the collision occurred</param>
    void HandleCollisionEvent(Collider2D collider, Vector2 hitPoint);
    
    /// <summary>
    /// Process collision with a specific target object.
    /// Called after collision validation to apply damage, effects, etc.
    /// </summary>
    /// <param name="target">The GameObject that was hit</param>
    /// <param name="position">The world position of the collision</param>
    /// <returns>True if the collision was processed successfully</returns>
    bool ProcessCollision(GameObject target, Vector2 position);
    
    /// <summary>
    /// Get the current collision radius for circle-based collision detection.
    /// Used by raycast helpers to determine collision bounds.
    /// </summary>
    /// <returns>The collision radius in world units</returns>
    float GetCollisionRadius();
    
    /// <summary>
    /// Get the layer mask for collision detection.
    /// Determines which layers this object can collide with.
    /// </summary>
    /// <returns>LayerMask for collision filtering</returns>
    LayerMask GetCollisionLayerMask();
    
    /// <summary>
    /// Check if this collision handler is currently active.
    /// Inactive handlers should not process collisions.
    /// </summary>
    /// <returns>True if collision processing is active</returns>
    bool IsCollisionActive();
}